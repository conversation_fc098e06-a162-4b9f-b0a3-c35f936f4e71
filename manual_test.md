# Manual Test for Fixed E2B Sandbox

## Step 1: Start the Server
```bash
cd /Users/<USER>/Desktop/projects/AI_projects/autonomous-coding-agent
python -m uvicorn src.api.main:app --reload --port 8000
```

## Step 2: Test the PR Creation
Open a new terminal and run:

```bash
curl -X POST "http://localhost:8000/code" \
  -H "Content-Type: application/json" \
  -d '{
    "repo_url": "https://github.com/alhridoy/CSDR",
    "prompt": "Add comprehensive error handling with try-catch blocks for database operations. Include proper error logging, user-friendly error messages, and retry mechanisms for transient failures.",
    "branch_name": "error-handling-improvements",
    "pr_title": "Add comprehensive error handling and retry mechanisms"
  }'
```

## Step 3: Alternative Test with Different Prompt
```bash
curl -X POST "http://localhost:8000/code" \
  -H "Content-Type: application/json" \
  -d '{
    "repo_url": "https://github.com/alhridoy/CSDR",
    "prompt": "Implement rate limiting middleware to prevent API abuse. Add request throttling, IP-based limiting, and proper HTTP 429 responses with retry headers.",
    "branch_name": "rate-limiting-middleware",
    "pr_title": "Implement rate limiting and API throttling"
  }'
```

## Step 4: Check for Success Indicators

### ✅ Expected Success Indicators:
1. **Repository cloned successfully** - Shows file count > 0
2. **E2B sandbox initialized** - Log shows "🚀 API: Using E2BSandbox for PR creation"
3. **Git operations succeed** - Branch creation, commits, and push work
4. **PR creation succeeds** - Shows pr_created event with URL
5. **Clean GitOperationEvent output** - No more misleading "fatal" errors

### ❌ What to Watch For:
- Any "failed to get remote URL" errors
- GitOperationEvent showing misleading stderr
- PR creation failing after successful push

## Step 5: Verify on GitHub
Check https://github.com/alhridoy/CSDR/pulls for the new PR

## Expected Output Format:
```
data: {"type": "Status", "message": "Initializing coding session..."}
data: {"type": "Status", "message": "Cloning repository: https://github.com/alhridoy/CSDR"}
data: {"type": "Status", "message": "Repository cloned successfully. Found XX files"}
...
data: {"type": "Tool: Bash", "command": "git checkout -b error-handling-improvements", "output": "Switched to a new branch 'error-handling-improvements'"}
data: {"type": "Tool: Bash", "command": "git add .", "output": ""}
data: {"type": "Tool: Bash", "command": "git commit -m '...'", "output": "[error-handling-improvements abc123] ..."}
data: {"type": "Tool: Bash", "command": "git push origin error-handling-improvements", "output": "..."}
data: {"type": "pr_created", "pr_url": "https://github.com/alhridoy/CSDR/pull/XX"}
```

## The Fix You Made:
Your E2B sandbox fixes ensure:
- ✅ Proper session storage initialization
- ✅ Repository paths stored and retrieved correctly  
- ✅ Git operations use absolute paths with `git -C`
- ✅ Remote URL retrieval works reliably
- ✅ PR creation succeeds consistently

This should create a new PR on your CSDR repository, demonstrating that your E2B sandbox fix is working correctly!