
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evaluation Report - abc_eval_20250720_143819</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .metric-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        .status-pass {
            color: #27ae60;
            font-weight: bold;
        }
        .status-fail {
            color: #e74c3c;
            font-weight: bold;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }
        .test-result.passed {
            border-left-color: #27ae60;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        .recommendation {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .tier-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .tier-ready {
            background: #d4edda;
            color: #155724;
        }
        .tier-not-ready {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Autonomous Coding Agent Evaluation Report</h1>
        <p>Generated: 2025-07-20 14:38:19</p>
        
        <h2>📊 Summary Metrics</h2>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-value">4</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" style="color: #27ae60">3</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" style="color: #e74c3c">1</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">75.0%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">155.0s</div>
                <div class="metric-label">Avg Execution Time</div>
            </div>
        </div>
        
        <h2>🏆 Backspace Metrics</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>Streaming Compliance</td>
                <td>85</td>
                <td><span class="status-pass">
                    ✓ Ready
                </span></td>
            </tr>
            <tr>
                <td>PR Quality Score</td>
                <td>66.25</td>
                <td><span class="status-pass">
                    ✓ Ready
                </span></td>
            </tr>
            <tr>
                <td>Specification Adherence</td>
                <td>72.5%</td>
                <td><span class="status-fail">
                    ✗ Not Ready
                </span></td>
            </tr>
            <tr>
                <td><strong>Production Ready</strong></td>
                <td colspan="2"><span class="status-fail">
                    ✗ NO
                </span></td>
            </tr>
        </table>
        
        <h2>🧪 Test Results</h2>

        <div class="test-result passed">
            <h3>demo_simple_readme</h3>
            <table>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="status-passed">PASSED</span></td>
                </tr>
                <tr>
                    <td><strong>Execution Time:</strong></td>
                    <td>90.00 seconds</td>
                </tr>
                <tr>
                    <td><strong>PR URL:</strong></td>
                    <td>https://github.com/example/repo/pull/861</td>
                </tr>
                
            </table>
        </div>

        <div class="test-result passed">
            <h3>demo_health_endpoint</h3>
            <table>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="status-passed">PASSED</span></td>
                </tr>
                <tr>
                    <td><strong>Execution Time:</strong></td>
                    <td>150.00 seconds</td>
                </tr>
                <tr>
                    <td><strong>PR URL:</strong></td>
                    <td>https://github.com/example/repo/pull/638</td>
                </tr>
                
            </table>
        </div>

        <div class="test-result ">
            <h3>demo_improve_performance</h3>
            <table>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="status-failed">FAILED</span></td>
                </tr>
                <tr>
                    <td><strong>Execution Time:</strong></td>
                    <td>180.00 seconds</td>
                </tr>
                <tr>
                    <td><strong>PR URL:</strong></td>
                    <td>None</td>
                </tr>
                <tr><td><strong>Error:</strong></td><td>Task specification too vague; No measurable success criteria; Expected outcomes not specific enough</td></tr>
            </table>
        </div>

        <div class="test-result passed">
            <h3>demo_input_validation</h3>
            <table>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="status-passed">PASSED</span></td>
                </tr>
                <tr>
                    <td><strong>Execution Time:</strong></td>
                    <td>200.00 seconds</td>
                </tr>
                <tr>
                    <td><strong>PR URL:</strong></td>
                    <td>https://github.com/example/repo/pull/322</td>
                </tr>
                
            </table>
        </div>

        <h2>📈 Tier Performance</h2>
        <table>
            <tr>
                <th>Tier</th>
                <th>Tests</th>
                <th>Success Rate</th>
                <th>Avg Time</th>
                <th>Status</th>
            </tr>

            <tr>
                <td>TIER1_BASIC</td>
                <td>2</td>
                <td>75.0%</td>
                <td>120.0s</td>
                <td><span class="tier-ready">✓ Ready</span></td>
            </tr>

            <tr>
                <td>TIER2_INTERMEDIATE</td>
                <td>2</td>
                <td>50.0%</td>
                <td>175.0s</td>
                <td><span class="tier-not-ready">✗ Not Ready</span></td>
            </tr>

        </table>
        
        <h2>💡 Recommendations</h2>

        <div class="recommendation">
            CRITICAL: 1 false positives detected - implement stricter PR validation
        </div>

        <div class="recommendation">
            Improve task specifications - 1 tests have ambiguous requirements
        </div>

        <div class="recommendation">
            Continue using ABC-compliant evaluation to maintain benchmark quality
        </div>

    </div>
</body>
</html>
