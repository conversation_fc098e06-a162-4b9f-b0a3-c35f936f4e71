#!/usr/bin/env python3
"""
Quick check for basic issues with the autonomous coding agent
"""
import os
import sys
import requests
import subprocess
from pathlib import Path

def check_env_vars():
    """Check if required environment variables are set"""
    print("🔍 Checking environment variables...")
    
    required_vars = [
        'ANTHROPIC_API_KEY',
        'GITHUB_TOKEN', 
        'E2B_API_KEY'
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
        else:
            print(f"✅ {var}: {'*' * 10}...{os.getenv(var)[-4:]}")
    
    if missing:
        print(f"❌ Missing environment variables: {missing}")
        return False
    
    print("✅ All required environment variables are set")
    return True

def check_server_running():
    """Check if the server is running"""
    print("\n🔍 Checking if server is running...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and responding")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

def check_processes():
    """Check for running uvicorn processes"""
    print("\n🔍 Checking for running processes...")
    
    try:
        result = subprocess.run(['pgrep', '-f', 'uvicorn'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ Found {len(pids)} uvicorn process(es): {pids}")
            return True
        else:
            print("❌ No uvicorn processes found")
            return False
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return False

def check_port():
    """Check if port 8000 is in use"""
    print("\n🔍 Checking port 8000...")
    
    try:
        result = subprocess.run(['lsof', '-i', ':8000'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Port 8000 is in use:")
            print(result.stdout)
            return True
        else:
            print("❌ Port 8000 is not in use")
            return False
    except Exception as e:
        print(f"❌ Error checking port: {e}")
        return False

def check_dependencies():
    """Check if key dependencies are installed"""
    print("\n🔍 Checking dependencies...")
    
    try:
        import fastapi
        import uvicorn
        import anthropic
        from e2b_code_interpreter import Sandbox
        print("✅ All key dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def main():
    print("🚀 Running basic issue checks...\n")
    
    checks = [
        ("Environment Variables", check_env_vars),
        ("Dependencies", check_dependencies),
        ("Server Running", check_server_running),
        ("Processes", check_processes),
        ("Port Usage", check_port),
    ]
    
    results = {}
    for name, check_func in checks:
        results[name] = check_func()
    
    print("\n📊 Summary:")
    for name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {name}")
    
    if all(results.values()):
        print("\n🎉 All basic checks passed!")
    else:
        print("\n⚠️  Some checks failed. See details above.")

if __name__ == "__main__":
    main()
