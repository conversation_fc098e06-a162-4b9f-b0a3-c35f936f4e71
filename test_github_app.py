#!/usr/bin/env python3
"""
Test GitHub App authentication with gh-token
"""
import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sandbox.e2b_sandbox import E2BSandbox
from src.utils.config import get_settings
from dotenv import load_dotenv

load_dotenv()

async def test_github_app_auth():
    """Test GitHub App authentication"""
    print("🔍 Testing GitHub App authentication...")
    
    # Get settings
    settings = get_settings()
    
    # Check if GitHub App credentials are configured
    if not all([settings.github_app_id, settings.github_app_installation_id, settings.github_app_private_key]):
        print("❌ GitHub App credentials not configured in .env")
        print("Please set:")
        print("- GITHUB_APP_ID")
        print("- GITHUB_APP_INSTALLATION_ID") 
        print("- GITHUB_APP_PRIVATE_KEY")
        return False
    
    print(f"✅ GitHub App ID: {settings.github_app_id}")
    print(f"✅ Installation ID: {settings.github_app_installation_id}")
    print(f"✅ Private key: {'*' * 20}...{settings.github_app_private_key[-20:]}")
    
    try:
        # Create E2B sandbox
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        print(f"✅ E2B session created: {session_id}")
        
        # Test GitHub App authentication
        auth_result = await sandbox.setup_git_auth(
            session_id,
            settings.github_token,  # PAT as fallback
            app_id=settings.github_app_id,
            installation_id=settings.github_app_installation_id,
            private_key_content=settings.github_app_private_key
        )
        
        print(f"✅ GitHub App auth result: {auth_result}")
        
        if auth_result.get('success') and auth_result.get('method') == 'github_app':
            print("🎉 GitHub App authentication successful!")
            
            # Test gh auth status
            status_result = await sandbox.execute_command(session_id, "gh auth status")
            print(f"✅ GitHub CLI status: {status_result.get('stdout', 'No output')}")
            
            # Test API access
            api_result = await sandbox.execute_command(session_id, "gh api user")
            print(f"✅ GitHub API test: {api_result.get('stdout', 'No output')}")
            
            await sandbox.cleanup_session(session_id)
            return True
            
        else:
            print("❌ GitHub App authentication failed")
            await sandbox.cleanup_session(session_id)
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pr_creation_with_app():
    """Test PR creation with GitHub App authentication"""
    print("\n🔍 Testing PR creation with GitHub App...")
    
    settings = get_settings()
    
    try:
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Clone a test repository
        repo_result = await sandbox.clone_repository(
            session_id,
            "https://github.com/alhridoy/CSDR"
        )
        
        if not repo_result["success"]:
            print(f"❌ Clone failed: {repo_result}")
            return False
            
        print(f"✅ Repository cloned: {repo_result['repo_path']}")
        
        # Setup GitHub App auth
        auth_result = await sandbox.setup_git_auth(
            session_id,
            settings.github_token,
            app_id=settings.github_app_id,
            installation_id=settings.github_app_installation_id,
            private_key_content=settings.github_app_private_key
        )
        
        if not auth_result.get('success'):
            print(f"❌ Auth failed: {auth_result}")
            return False
            
        print(f"✅ Auth successful: {auth_result.get('method')}")
        
        # Create test branch
        branch_name = "github-app-test"
        repo_path = repo_result["repo_path"]
        
        branch_result = await sandbox.execute_command(
            session_id,
            f"git -C {repo_path} checkout -b {branch_name}"
        )
        print(f"✅ Branch created: {branch_result.get('success')}")
        
        # Make a small change
        await sandbox.execute_command(
            session_id,
            f"echo '# GitHub App Test' >> {repo_path}/GITHUB_APP_TEST.md"
        )
        
        # Commit changes
        await sandbox.execute_command(session_id, f"git -C {repo_path} add .")
        commit_result = await sandbox.execute_command(
            session_id,
            f"git -C {repo_path} commit -m 'Test GitHub App authentication'"
        )
        print(f"✅ Commit result: {commit_result.get('success')}")
        
        # Push branch
        push_result = await sandbox.push_branch(session_id, branch_name)
        print(f"✅ Push result: {push_result}")
        
        if push_result.get('success'):
            # Create PR
            pr_result = await sandbox.create_pull_request(
                session_id,
                "Test GitHub App Authentication",
                "This PR tests GitHub App authentication with gh-token",
                branch_name
            )
            print(f"✅ PR result: {pr_result}")
            
            if pr_result.get('success'):
                print(f"🎉 PR created successfully: {pr_result.get('pr_url')}")
                await sandbox.cleanup_session(session_id)
                return True
        
        await sandbox.cleanup_session(session_id)
        return False
        
    except Exception as e:
        print(f"❌ PR creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 Testing GitHub App authentication with gh-token\n")
    
    # Test 1: Basic GitHub App auth
    auth_success = await test_github_app_auth()
    
    if auth_success:
        # Test 2: Full PR creation
        pr_success = await test_pr_creation_with_app()
        
        if pr_success:
            print("\n🎉 All tests passed! GitHub App authentication is working!")
            print("Your E2B sandbox should now create PRs successfully.")
        else:
            print("\n❌ PR creation failed even with GitHub App auth")
    else:
        print("\n❌ GitHub App authentication failed")
        print("Please check your .env configuration")

if __name__ == "__main__":
    asyncio.run(main())