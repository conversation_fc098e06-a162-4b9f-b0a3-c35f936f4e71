#!/usr/bin/env python3
"""
Manually create the PR since the branch was pushed successfully
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sandbox.e2b_sandbox import E2BSandbox
from src.utils.config import get_settings


async def create_pr_manually():
    """Create the PR manually with clean description"""
    
    try:
        settings = get_settings()
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Setup git auth
        await sandbox.setup_git_auth(session_id, settings.github_token)
        
        # Clone the repo to get context
        repo_result = await sandbox.clone_repository(session_id, "https://github.com/alhridoy/CSDR")
        repo_path = repo_result.get('repo_path')
        
        # Switch to the pushed branch
        await sandbox.execute_command(session_id, f"git -C {repo_path} checkout feature/enhance-readme-documentation")
        
        # Create PR with clean description (no emojis to avoid syntax error)
        pr_title = "Enhance README with comprehensive project overview and badges"
        pr_body = """# Enhanced README Documentation

## Summary
This PR significantly improves the README.md file with comprehensive project documentation.

## Changes Made
- Added project overview and description
- Added feature highlights
- Added quick start guide
- Added repository structure diagram
- Added contributing guidelines
- Added proper badges (build, license, version)
- Added author information
- Improved formatting and visual appeal

## Benefits
- Better first impression for new visitors
- Clear project understanding
- Encourages contributions
- Professional documentation standard

## Test Plan
- README renders correctly on GitHub
- All links work properly
- Badges display correctly
- Formatting is consistent

---
Generated by Autonomous Coding Agent"""
        
        # Use gh CLI to create PR
        pr_result = await sandbox.execute_command(
            session_id,
            f'cd {repo_path} && gh pr create --title "{pr_title}" --body "{pr_body}" --head feature/enhance-readme-documentation --base main'
        )
        
        if pr_result.get('success'):
            pr_url = pr_result.get('stdout', '').strip()
            print(f"🎉 SUCCESS! Pull request created:")
            print(f"📎 {pr_url}")
            return True
        else:
            print(f"❌ PR creation failed: {pr_result}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        await sandbox.cleanup_session(session_id)


async def main():
    success = await create_pr_manually()
    if success:
        print("\n🏆 PULL REQUEST CREATED SUCCESSFULLY!")
        print("Your autonomous coding agent is fully operational!")
    else:
        print("\n⚠️ Could not create PR automatically")
        print("But the branch was pushed - you can create PR manually on GitHub")


if __name__ == "__main__":
    asyncio.run(main())