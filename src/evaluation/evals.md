The team at Priceline discussed evaluations (evals) primarily in the context of their Penny Generative AI agent, particularly concerning its content output moderation.
Here's what they talked about and the evals they used:
•
Content Output Moderation Evals:
◦
They implemented an eval process for content output moderation. This was chosen over input moderation because the messages move too quickly for effective input moderation and was a recommendation from OpenAI.
◦
The process involves checking if <PERSON>'s output content requires moderation.
◦
If moderation is required, they take the transcript of <PERSON>'s output and use a separate Large Language Model (LLM) call to assess its validity or compliance. This helps them determine if <PERSON> "did a good job".
•
Telemetry for Running Evals:
◦
While their initial real-time voice telemetry setup was not ideal (a single span for each session rather than each transaction), it still provided "so much valuable data" and made development "a lot easier".
◦
They are currently working on an update to their telemetry system to be transaction-level based, which was suggested by Arize AI and makes it "so much easier to run evals and stuff on". This indicates that effective data collection is crucial for their evaluation processes.
•
Future Enhancements for Evals:
◦
A significant upcoming enhancement involves the ability to link to recorded audio in the user interface. This will allow them to "write evals against things that we just wouldn't be able to write evals about" if they didn't have access to the actual audio.

---
hi this is <PERSON> heluk from root signals uh presenting agent evaluations finally with the map so the agent evaluation is rather Art and Science and ultimately it is nonetheless required to actually ensure that your agents do what you expect them to do especially when you're launching them in production so without further Ado let's just uh Dive Right There into the map and start to get an understanding of what are all the things involved if you want to evaluate all the aspects of your agents uh so the agent evaluation can be actually neatly divided to the semantic and behavioral parts of what the agents do the semantic part is all about how do the representations uh of the reality that the agent has actually relate to the reality whereas the behavioral parts are all about how do the actions and the tools that the agent is using actually contributing to the achievement of the agent's goals in its environment and ultimately what kind of effects it will have on its environment as well the semantic part is uh further divided into what could be call the single step or single turn uh items which are like coherence consistency Etc so various sematic virtues and what can be called the multi- turn aspect of the same so this Rel to chatter reasoning whereas in the behavioral part you also have this distinction whether you're looking at the task progression and planning or then individual selection and usage of tools so let's uh dive into each of these and paying also attention to the fact that truthfulness is ultimately achiev by grounding uh the representations in your data often with track whereas uh the goal achievement is what actually uh is achieved by grounding to the tools that the agent has available and the this is sort of a symmetry that is not accidental because in a sense there's similarity and analogy between uh representations and behaviors because representing the world is a kind of activity so you can see that uh that the representations are in a sense a special case of tools special case of uh uh of behaviors and uh let's then dive into these individual parts of the map so first looking at the semantic quality for the single turn case so here we have these Universal virtues there's a long list of these that you can look at uh and these are actually non agenic in a sense and we are only covering them here because of completeness so that you can sort of understand these agent Parts through the contrast so these are things like uh uh is the uh the reply that the agent is giving to the user is this consistent is the is the content actually safe and so on and so on and then there's the inter interesting and important part of whether whatever the agent is actually saying if it's uh aligning uh with the values of uh of the organizations or people uh who are the stakeholders and also whether it aderes to the policies of the same rag or what could be more generally called yet attention management uh is then something that you need to measure through specific evaluators such as those looking at whether the retreat context uh was correct whether all of it was comprehensively recalled uh and so on and so on and ultimately relating the answers uh to the external reality through what is often called the Fai fulness which is then separate from uh from the both the answer question relevance and also from the notion of factfulness in general which relates to the reality Beyond just the reference data that the rag is using so now moving forward uh we should also pay attention to the fact that the rag evaluations actually come uh in many forms and they they also have certain symmetries uh that are not that difficult to understand when you look at them uh through uh a map like this so they are all just essentially looking at different relationships between the parts of the rag pipeline but this is not the the main topic of the presentation so you can look look up more information through the uh background material links that people provide so then moving to the multi-turn case the multi-turn in the semantic quality sense means essentially chat conversation histories how do these develop what sort of things we want to look for in this consistency Etc adherence and sticking to the topics when that is actually necessary sometimes we want to allow changing topics if the for example a chatbot users user wants to change the topic or sometimes we don't but we need to be able to uh be aware of this then super important uh and almost groundbreaking progress has been achieved by uh by uh investments in reasoning so you can also uh actually evaluate reasoning traces the reasoning uh Chain of Thought if if allowed to use that term here and that's sort of another way of looking at the kind of sequential or M well multi- turn and sequential kinds of activities that the agent is just doing in the course of its reasoning and representations of the world before we even taking any kind of actions and now we move to the side so here we have uh things to evaluate such as where the agent is actually following instructions is it extracting the the tool characteristics correctly is it selecting the right is the right tool being selected is the output quality of the tools correct are the error situations handled correctly are the structures interation tool format are they uh correct so these are all all the things that relate to uh to the behaviors even before you have a chain of uh behaviors and when you move to this chain of behaviors or multistep or uh multi-turn uh cases so then you actually start to look at like are the actions that the ACT agent is taking converging towards actually achieving its goal and uh is the plan that the agent might have actually consistent and is it actually uh high quality in whatever sense you want to measure that uh so then both of these are actually grounded uh on external reality so uh like mentioned the representations are grounded on truthfulness and uh the activities and behaviors are ultimate grounded by goal achievement and utility uh and this is uh what sort of uh is the ultimate uh metric to everything else and know uh that the agent is trying to do and all these other things along the way are sort of just proxy metrics so to speak uh moving to other practical considerations we can only scratch the surface here so I'm just sort of listing not to leave this out but they don't needly fall into this general map that I presented but should also be taken into account on the background so most important of which is the cost and latency optimization so generally you want to the agent to progress towards its goal as quickly as possible and as cheaply as possible so cost and latency optimization the number of steps optimization and then moving to the what you ofer are going to need which is the tracing and debugging so being able to actually see where the agent go wrong error management here refers to to dealing with the with the errors of the tool usage so not the actual like semantic errors that that the agent is doing in the course of its inference and thinking process uh very important distinction to keep in mind is the offline versus online testing so what are the things that uh that you can actually uh try out and evaluate during development and what are the things that you can do and must do or should do uh during the actual online activities of the agent and these are actually going to become like a two very distinct Dimensions that could be also included on the map but uh but that would make the map map more complicated so that's why I'm just mentioning them here now separately and then we have various special cases that I don't have time to cover some of these uh are uh more refined and more advanced than others and and some of these things are sort of uh more researchy and there's a lot of papers on each of these topics and uh uh depending on what you're doing tool specific metrics might actually be useful to add to the mix even uh uh and they might even be rather simple to implement because the tools are often something as easy as straightforward as like API calls and uh and such things that can actually be just measured separately uh using more traditional software testing methodologies uh one important part to mention here is that a lot of these measurements are going to to be implemented with llm as charge techniques and now the caveat uh behind this is that the quite often when people start implementing these evaluation methodologies they are looking at this kind of a single tier approach single tier here means that you're focusing on optimizing this operative element flow meaning that you're optimizing your agent which makes sense but if you only are concerned of uh with getting some scores on what the agent is doing then you're sort of forgetting that what about all the cost latencies uncertainty related to the charge itself the charge that is on the background so you should be actually uh taking into account as early as possible that you are going to need to also optimize the charge itself and this is what the what we could call Double tier so you need to optimize both the operative LM flow that powers your agent and then this chargement flow that actually Powers your evalu ations and uh this is a rather complex situation in general and we are calling this eval Ops because uh this seems like a like a separate kind of thing that involves uh evaluations that themselves are so complicated uh so expensive and slow that they sort of earn their own uh category of of activities and this is uh something that uh that we have written about and uh and you can also find more about this on the source materials uh the general general tast of it is that that eval Ops is a kind of a special case of llm Ops and and actually operates on different kinds of entities that that the llm Ops uh uh in general and uh and then it also requires different ways of thinking different kind of software implementations and also essentially a different kind of resourcing to to get those evaluations right so thank you very much this was only a brief Glimpse on the on the general landscape I hope the map is helpful to you and uh please take a look at the source materials which will give you more depth to each of these uh these topics and uh happy to discuss any of these things and let me uh know if I'm forgetting something crucial and uh this uh presentation will of course be obsolete by the time it it goes out so uh probably when when you're watching this there's already some major developments have happen but that's uh that's how it is on this General Life of uh of uh AI Engineers uh so let's go out there and make our agents measurable controllable and let's make sure they are actually doing our biding and not not rebelling against our uh ultimate intentions thank you very much 

-----
 
Me: By spirit  
Them: My experience building an agent for SuiteBench verified is kind of our case study. SuiteBench verified for those that aren't particular is kind of the canonical AI coding evaluation. We'll we'll get into a little bit more what that is as a refresher a few slides in. Here. Let me set my timer, actually, just to make sure we're on time. K. So okay. AI coding. Let's take a kind of zoom out and and think about what's been going on in the space for the past couple of years. So 2023 was completion models. So this is, like, GitHub Copilot. 2024 was when people started using chatbots to help write code. This is in particular when people found that, like, Cloud, it was really helpful when it could, like, look at your code base or look at files. 2025 is really about agents. Now if we look under the hood and think about the underlying retrieval tasks that are relevant to each of these products, the retrieval complexity is only getting higher. So for completions, the kind of deciderata for retrieval system is like okay. It needs to be super low latency, so that's hard. But the retrieval is not super difficult for code at least. So it's like, I'm trying to complete a function. Let's say it's a implementation of a method that lives on the interface. Well, I probably wanna go retrieve other, implementations of that interface, for inspiration. And so you kinda just need to, like, copy words, like, look for, like, other locations where that function name is present. And that's clearly not that hard of retrieval task. The only thing that's annoying is how latency sensitive it is. Then we go to chat box in 2024, And with chatbots, things are getting a little bit harder. I may have a question like, okay. Let's say I'm building kind of like an LLM power application. I may have a question from my chatbot, like, how, do I abstract away the LLM SDK that I'm using from my application. So what that means is, you know, maybe I wanna use OpenAI model sometime, some I wanna use Gemini models, and I wanna have, like, one consistent API for all these and not have to use the Gemini SDK sometimes and the OpenAI SDK and other times. And so that probably means, you know, creating some kind of interface on top of on top of the, OpenAI client and on top of the Gemini client. And then the question is, is you know, okay. That were that that chat model, can it find the locations of the Gemini SDK and the OpenEye SDK in your code base, can it kinda figure out, like, okay. How do I abstract these? And can I figure out maybe where should I put that abstraction? And this is a bit more of a challenging retrieval problem. And so here was when we really found, like, training embedding models becomes a little bit more important. Now with agents, it's getting a lot more complicated. Now the agent has to understand many different parts of your code base. It has to through different files. It say, like, looks through a 100 k tokens worth of files, and then it needs to truncate those from the prompt and go look at another 100 k worth of tokens. So things are only getting more complicated. A quick refresher on on what exactly do we mean by retrieval here. So, you know, everyone thinks about embedding models, but there's really a lot of different techniques that could be used. There's good old BM 25, which, by the way, for completions, we found was extremely good. There's embedding models, which have a little bit more flexibility or good for less structured content. Good when you're not doing just similarity, but you're doing some kind of more complex mapping you're trying to model in the retrieval. And then there's, like, other tools like GREP and PageRank. Quick plug on, you know, this work. So as I mentioned, we were making this agent that could perform on this eval called a SWE bench. So SuiteBench is essentially, like, I give, the agent a description of a coding problem, that has access to a code base. There are some held out tests and some not held out tests, and then the agent has to go implement the that PR description in that code base, and then we run all the tests at the end and see if it passes. It's able to do things like you know, use whatever tools we we provide it, whether it's iterating through files or or running tests. So we provide we created this kind of open source repo for this, and it has all this simple stuff you may need. It has a bash, bash tool. File viewing tools. It has this notion of, like, a sequential thinking tool. And then everything else, like Docker harnesses so that the agent can run code and so forth. We ended up getting to the top of this eval, So you see that's that's us there up top, Augment agent v zero. One of the most interesting facts about this work was that we just gonna read this quote from our blog. We explored adding various embedding based retrieval tools but found that for Sweebench tasks, this, was not the bottleneck. GREP and find were sufficient. So this is really interesting, and frankly, the first time that I saw this result, I was, like, surprised. I I thought maybe there there's, like, a bug in our evals or something because, like, clearly embedding models should be so much more powerful than grep and find. And so we'll we'll kind of look at some examples and try to figure out. You know, does this data point indicate that, you know, traditional reg as we know it is is obsolete. So here we're looking at, like, a very specific SWE bench problem. So I'm just gonna read through it so we can get some context. So this is pulled from an actual PR in GitHub. An actual issue on GitHub. Modeling separability matrix does not compute separability correctly for nested compound models. Consider the following model, and there's some code. Acceptability matrix as you might expect, is diagonal. It's a more code. I make the model more complex and more code. And then user starts talking about how in a particular situation, these inputs, outputs are no longer separable. And it's not really important here is the separability matrix, but, like, just you know, it's good to kinda get a sense of what is the problem. So there's this code base called Astropy. There's this function called separability matrix in this file. And there's some property of it that seems buggy. So now let's look at what the agent does to try to solve this problem. So here, I actually took all of our agents' tool calls and LLM responses and, and and kinda just paste to them here. We can kinda click through these and and and kinda get a sense of of what it's doing under the hood. So first, you know, we we have this initial prompt, which which obviously contains all this, and we ask the agent to to figure out how to fix it. Then then the agent responds, I'll help you implement the necessary changes to fix the issue with the separability matrix function for nested compound models. Let's start by exploring the repository structure to get familiar with it. Then it calls a bash tool using find and it looks for all files that have the prefix in the name set so it's like separability, separable, and then it finds this separable dot py file, and then it uses a file reading tool to look at that file. And then it says, now let's also look at the compound model class to understand how it's structured. It looks in this file core dot py. And it greps for the word modeling. Ben doesn't quite find anything, so now uses a file reading tool. And it reads lines one to 50 for port dot pi You'll notice here it's just reading 50 lines instead of reading all the file which you did separable for separable dot py. One, because this is an option. It can optionally just look at a few lines. But also, and it's prompted to try to, you know, as little token budget as possible, but, also, this is a much larger file. It doesn't find what it's looking for, and so it's like, let's look at the compound model class. It does a grep. It still doesn't find what it's looking for. It does another file read. Still doesn't find what it's looking for and does another grep. Still doesn't find what or actually, it finds it then. And then and then now it's, like, looking for the function, so now it does another grep. And so, you know, and then and then it kinda goes on and it does implement the the correct solution. And so my takeaway looking at this was, well, agents are kinda dumb. But they're persistent. So they eventually find their way around with GREP and FIND. In other words, the agent's persistence is compensating for worse tools. So there are some benefits of this agentic retrieval with GREP and FIND. One, iterative retrieval is super easy. So if we if any of you have seen what, like, an old school iterative retrieval setup might look like, it's something like in this bottom right. You have a prompt one, which is your query. You put that in embedding model. You dot product against the knowledge corpus. Get some top k documents. Now you have new prompt that's query plus those top k documents. You put that into a new a second embedding model, which you've trained differently, You dot product that against the  
Me: Your  
Them: to  
Me: Okay.  
Them: Talk it. And then you have to decide how to balance your resultant prompt between the set of top k documents and this set of top k documents. Prompt token budget management is a lot easier. So this is some code from our code base and, basically, what happens is, you know, the agent's iteratively doing this this crazy and grabbing all these different files. And anytime we hit our token budget limit, which is I think it was a 150 k tokens, we just truncate all the old tool calls. So here, we replace all the sequential tool calls with truncated. We run tool if you need to see output input output again, and then we do the same for other for other tools. And so as a result, the agent, you know, can use up all of its prompt space. It never, hits any any kind of errors with that. And if it needs to go look at the output of some old tool call, like some old grep, for example, can just rerun that grep. It's super low effort to build and maintain. So with vector databases, you obviously have this whole new dependency and this whole new database you have to deal with. You have, say, a sync you have to manage. So, you know, with completions, we have, say, like, fifty millisecond service level agreement for how quickly we update the documents on the client. So in that Versus Code editor or whatever your editor is, and and our servers, None of that you have to worry about anymore. And finally, there's this really cool course correction property. Where if the first call to say the bachelor tool with find or or with grep doesn't work, then it tries again, and maybe this is a view tool. Then it tries again. It uses it uses grep. But the challenges with grep and find are they're hard to scale, So imagine doing GREP and FINE against 10,000,000 files. What about a 100,000,000 files? You definitely can't replace PageRank on Google with with Grep and Find. And they also don't work particularly well with low structure natural language. So if we look at the bottom right here, it's just some random code snippet I picked out There are all these high signal words in it. So there's text prompt, text results, token counter, count tokens, and these make it really easy to figure out how to grep things, like again, that go back to this there's, like, sepra star. Like, that works because code is super is super structured. That's not necessarily true for, like, other kinds of content. Or or information you you might wanna retrieve over. So what's the best of both worlds? You can imagine this agentic loop, but with access to great embedding models is as tools. In order to build this you still build your underlying embedding system per usual, and you just surface it to your agent as a tool similarly how we to how we surface GREP and find these tools. There are gonna be some potholes when applying this to other domains. One of my hypotheses that I haven't you know, really confirmed but I think maybe true is that anthropic models are post trained to be really good at agentic code search specifically, but I'm not sure how well they that generalizes to other domains. As I just mentioned, code has a lot of high signal keywords. And also all of this is is pretty slow and expensive. So when we are trying to decide then, okay, how do we architect this retrieval system? We may have some rag. So the traditional RAG may have this agentic retrieval. The embedding tool is one option. The Grep and find tools are other options. There are different axes we can consider, and each of these axes is different weight depending on what you're building. So there's quality, so how good is the the final output, There's latency, so it's you know, the embedding models, you know, as I mentioned, are, like, fifty millisecond SLA. Agents can take many minutes. Cost, so the agents are much more agentic, which was much more expensive. Reliability, agentic retrieval has that course correction. But embeddings do not. Their size of index, so rep and find work totally great for, you know, medium sized indices, but for truly large small to medium sized indices, but for truly large indices, it's just too slow. And then there's the effort to build and maintain the your retrieval system Do you have the the engineering resources to deal with maintaining some index and updating it and whatnot? So we look at some examples of some different kind of configurations of systems, so, again, traditional retrieval, it's okay quality, it's really fast, it's really cheap, The reliability isn't great because there's no course correction. It's super scalable, and it kinda depends on the effort to build and maintain it. The main factor here is, I think, whether or not you're training your own embedding models. This kind of agent plus grep find tool, the quality is really great, but it may degrade in certain situations like really large indices, or when you're retrieving or searching over content that's super low structure where like, keyword searches don't work well. It's slow. It's expensive. The reliability is great because agents are persistent. They're course correcting. And this also is gets even much better when the agent has access to some kind of signal that it's in the right direction. So for coding tools, you can the agent has access to being able to run tests. And so if it gets kinda stuck in some direction and has some hypothesis for you know, kind of a task it's trying to complete, it can spin up an ephemeral file, run some code, see the output, and then use that to kind of inform how it continues to do the the code search. The size of the index, well, it's it's not that scalable as we talked about. But the effort to build and maintain it is is super easy. And then finally, this kind of third option I mentioned, which is the agent plus an embeddings based tool. The quality is great. It's still slow and expensive, but we get everything else. So  
Me: Issus. Hello?  
Them: get the reliability, persistent course correcting,  
Me: Okay. Okay.  
Them: It now handles large indices, and the effort to build and maintain the retrieval system. It's mostly easy because you have less of a need to train embedding models because agent can just be persistent. I know, you know, that depends. Maybe you wanna optimize things further. I have agentic loop and also a really good embedding model that can help. I wanna go through a couple, like, preemptive q and a's and then we can kind of talk through questions. So one, I already built a retrieval system with custom trained and Bing models. Do I check it and build agentic retrieval? It should be clear right now. No. Build agentic retrieval on top of it. Just take your existing system and add it as a tool to an agent loop. How do I eval agentic retrieval? End to end, vibe eval first. So five to 10 examples. Then consider quantitative end to end eval or eval just of the tool. Note that improving embedding tool won't necessarily help because the agent is persistent. So I'll give some anecdotes here from our experimentation. So were, like, working on an embedding based tool for SuiteBench in particular, And we had some evals that were using the kind of hill climb just on that embedding model. And we improved the embedding model, but we found that the end to end result didn't improve because the was super persistent. So you always wanna start with that end to end eval first and then kind of go from there depending on where where the system is struggling. I'm starting from scratch. Do I start? I'd suggest building the dumbest retrieval tools possible. Put an agent loop on top, iterate from here on whichever qualities of the system cause the most pain for customers or for you, and follow the previous eval instructions. And then the last question or last topic in traditional reg, I can improve my system by training better embedding models. How do I improve my system with AgenTeq retrieval? So in general, I would just say build better tools. So this is a little anecdote. Agents are getting radically smarter, but even Einstein preferred writing on paper instead of a stone tablet. So, yes, these agents are persistent. Whatever. You can give them to improve the odds that they find what they're looking for. So that could mean adding a re ranker to your embedding tool. So one benefit here is you spend less tokens, so it's cheaper. Maybe it's also faster because the agent messes up less because you have precision on that embedding tool is higher. You can try training different embedding models for different tasks. Maybe you have one embedding model that's really good at answering questions about messages in Slack. Another one that's really good at looking over the code base. You can provide these as separate tools. You also wanna prompt tune your tool schemas to avoid pitfalls. So for example, if we look at our bash tool, which as you as we saw previously, our SuiteBench agent was using to call grep and find and whatnot. We had to prompt tune a little to tell it, like, this is how you use grep. And, like, this is how you use find. And, like, by the way, like, don't do x and y regexes because those will be really slow. Yeah. So that's kinda that's kinda everything. I'll pause now, and we can kinda talk through any questions. Hopefully, that was all stimulating. This is great. I have a bunch of questions. In my mind, but let's go over some of the questions on the Slido. Marion just shared the link back in the chat. So feel free to just go on there, look at the questions you want to ask, upload the ones you you care about. I think the first question on everyone's mind is actually gonna be around the frameworks that you use. So how did you decide what tools to build? Did you build the framework yourself? You talk a little bit more about that? Definitely. You know, I so I built the framework myself. Let's go all the way back here. I've shared a link to the GitHub on chat. So I built I built the framework myself. I found you need something very simple. Like, it's very hard to you know, I looked at some of the existing frameworks out there, and they were nice. But I I just I wanted something super simple. Like, I can yeah. Let's see. I will can you still see my code base Yep. Or my screen? K. I'm just gonna, like, walk you through a few things in this code base just to give you an example. So we have this tools directory. There's this very simple notion of an LLM tool. Let's see. LLM tools in a UTills common. Utils common dot pi. Yeah. So there's this very simple abstraction. It's LLM tool. It has a name, a description, and input tool schema. There's, like, a few commands on it. Then you have this notion of a dialogue messages, which is where we keep track of, like, the the dialogue history for the agent. This needs to have options to add user prompts and then also agent prompts and tool calls. And then finally, need some kind of truncation strategy. And this is one thing that might be interesting to talk about is, like, how to think about truncation strategies. But once you have those components, you have that dialogue messages object, you have this LLM tool abstraction, then you're like, pretty much good to go. You yeah. That's pretty much it. Then you just call the the agent a loop. I for at the top level agent, I actually just implement the l this LLM tool extraction. It just has, like, separate LM tools. It's, like, the the dependencies. So given those are the only things that are necessary, like, I didn't really use any frameworks. Yeah. That makes sense. I think a lot of people sort of at least the high level practitioners have generally leaned on things yourself just because you want fine control over the prompts and how things are called and and whatnot. Those are just so simple too. Yeah. Is this I'm working on several new projects right now. And because I I I I left Augman recently to experiment on some different projects, and I built several agents, and I literally and for total things totally unrelated to code, and I literally just forked to this code base. And, like, made a handful of changes, and it, like, pretty much just works. Alright. So there's gonna be a new agent framework that you're gonna release sometime in the near future. Hold on. I'd love for you to comment on that, like, one conversation we had earlier like, last month where it was just, you know, do I give my agent, like, access of control, like, command line and have it do everything, or do you build out these specific, like, search tools like grab find and all that? You mentioned it sort of really changed the distribution of how these things behaved. Could you talk a little bit more about that too? Yeah. I mean, I guess like, I mean, one interesting thing here is, like, we don't really give it a grep find tool. We just give access to a bash tool, and it can kinda do whatever it wants And so you do wanna give it a a kind of this maximum flexibility And this works because it's super persistent. So, like, when it messes up, when it, like, runs the wrong command, then it will, like, figure out how to run the right command and and go from there. But but, yeah, again, like, if you if you already have a good embedding system, I wouldn't, like, throw it away. It's, like, probably gonna be better than than bash. Yeah. Does that does that kind of answer your question? I think so. I think it could have answers the question also of, like, do we want sort of a few very good tools or many tools in the beginning? And here, it really feels like Oh, right. Yes. Embeddings, and you're probably gonna be good to go. Yes. Okay. So, yeah, one interesting thing here is, like, people have talked a lot about tuning your system prompt and tuning your prompts. There's a whole new access to this now. With agents, which is, like, tuning the set of tools. Bash, we could have instead had, like, 10 different sub goals. And we actually did start with this, which was, like, having the file reading tool, file editing tool, the GREP tool, the find tool, and so forth. And then, you know, the agent call these tools whenever, I'm like, it kinda it didn't help. It made the code base more complex. Yeah. It it didn't seem like a very fruitful direction. I'd say. Interesting. It it almost feels like you kind of bitter lessened away. A lot of those things. And so you know what? Turns out if you just give a bash, it'll work today. Maybe in the future, it's just keyboard and mouse inputs. But Yeah. I I also think the more tools you get, the more you more stuff you have to prompt tune, and so that takes that just takes some more time. Got it. It's almost like fewer tools, fewer tools, better tools, and use tools that are very generalizable in in the hopes that future models will just be able to use them better. Yeah. Exactly. Makes sense. One question I'm really curious about is, like, what do you feel like we can learn as sort of practitioners not in cogeneration, but maybe, you know, what does the AI for medical scribes or the AI for, like, construction documentation or AI for legal like, for lawyers. Like, what can they learn from the things that the coding community has been, picking up on? Well, I mean, the main thing is to do AgenTik for Trickful. So that's, like, you give the agent access to tools that that are, like, search tools. The I'd say the next thing is, like, well, okay. How do I build those? How do I build that system? How do I build those tools? And the simple answer is, like, whatever you already have turn those into tools. And iterate from there. I think that's kind of the sim that's kind of the simple thing, which is, like, very unin maybe to some people because they want to go architect some whenever there's some big technological innovation, they wanna go architect some whole new system. Train models, and whatnot. But this is it's really just, like, a layer on top of your existing search tool existing search system. And does cost in money and it does cost in latency. It just kinda makes things better. So I would just start from there. And then, you know, there are all these as we kind of discussed a little bit, there are all these different directions you can go from there, whether it's, like, realizing that, okay, now these this agent is so persistent. I I wanna give it access to a a new kind of embedding model that, like, maybe it was really hard to fit into my previous search system. Maybe I discovered that, like, actually, my embedding models don't help at all. And doing some more basic search or doing, a Glean integration or something like that is just as effective and and way less total cost total cost of ownership for the team. So I just I just go to that and deprecate my embedding embedding service. You kinda need to make those decisions you know, once you kinda get a sense of of of what's moving the needle on on top of the basic agentic retrieval system. That makes sense. I think yeah, cost of ownership is something that people are not really thinking about these days, but really should as especially because the future, more and more things will be abstracted away. I think maybe last year, everyone was asking me how to fine tune language models. But now very little. Is going in that direction. But I think a lot more folks are thinking about doing fine tuning embedding models I mean, that's an interesting topic. I still think no one really knows how to fine tune embedding models. Like, very few people do. So that's one thing that's exciting about this kind of new technology is I can't tell you how many people have texted me over the past few years they heard Augment has, like, really good retrieval. They were like, how do we train good any embedding models for insert category x, y, or z, whether it's finance, or medicine or whatnot, or therapy. And it was kind of hard because I was just like, well, you need to have, like, an AI researcher on your team. And, like, there's not really any APIs for this. You can't there's nothing great out there. But now it's kind of like it's less important I do think this is kind of, like, in some sense, democratizing kind of, like, the a really good retrieval systems. Yeah. Yeah. I think Cursor is the only company I know who's really talked about fine tuning their embedding and that's mostly just for the tab model. Right? If you look at, like, the cursor agent, it's not really using embeddings that often in the agent product. But code completion, you really do want, like, very fast retrieval. Yeah. I mean, so we found fine tuning embedding models is really important. It it really does help. You know, I'll give you some examples reasons for why it helps. So we talked a little bit about, like, with code with with completions, it it's it's good, but also, like, code completions is, like, it's kind of this, like, keyword similarity. Task. But when you're, like, asking these more complicated questions, like, they're like, oh, I'm trying to, like, abstract away my LLM LLM SDKs. The embedding model needs to have, like, some understanding of, like, your particular domain. Like, it it needs to have, like, almost the reasoning capabilities of an LLM. Obviously, it's not going to because it's a much smaller model, it's, like, approximate that. And, like, most embedding models that you get off the shelf, that's not really what they're trained to do. They're trained to just, like, look for similarities between text a and text b. So it does really help. But it's just it's a little bit less of a bottleneck now that now that have this agentic retrieval. Makes sense. Makes sense. Question is pretty interesting because I think it exists in multiple scales. But what are your thoughts on memory, and how does it play with you know, other rags with coding agents? You know, I know there's compact as a way of truncation, but we'd love to hear a little bit more about that. That's a great question. I mean, obviously, memories are super important.  
Me: Right.  
Them: I guess well, how do you add memories? There's a really simple thing. I don't think this is anyone's secret sauce. You add a tool. That saves memories. Then you add a tool that reads memories. Now how does memories affect how we think about retrieval? Let's look again at this example. If we had memories here that kind of describe something like, at the end of this, you know, the model could create a memory that was something like, describing some aspects of the code base. Like, these, you know, these are the files where models are defined. And in general, like, models have these kinds of functions on them. Or, like, these are the relevant interfaces to consider. When you add this stuff into the memories, it speeds up the next time I go around the next time I try to do an agent run that kind of has a similar kind of search task as this. So yeah, so, you know, memories are personalization, but they're also in some way kind of like a, I guess, a semantic cache. That, like, speeds up future runs. Future code search or for future search runs. Yeah. This is some I I'd say kind of quick thoughts on it. Yep. I think that this is a big pretty big question, but can you also talk a little bit more about your eval harness? For this c bench work? Sure. Does success look like How do think about iterating on these things? Yeah. So it's really valuable to understand that, you know, even as you improve embedding model, it might not matter because the agent is so persistent. I feel like that's a big learning that Yeah. When once you say it. Yeah. I wasted a lot of time on this one. I mean, again, this is not to say that, like, embedding models aren't useful as tools that are very useful, but it's, like, not not as simple as, like, if you make score go up on embedding model eval, then score will go up on the end to end agent agentic retrieval eval. And so that's why I I I recommend to to start with the the end to end by eval eval first. I usually, in general, just recommend to start with five vowels. Vibe evals. I think this is something very unintuitive to people that come from, like, classical ML, who are always trying to make quantitative evals. Like, with natural language stuff, you can learn so much just from looking at a few examples. Maybe this is already kind of the consensus thing, but I've met a lot of people who still haven't really fully this. But, yeah, once you have a few examples and you get it for, how good is the system? What are the general kinds of questions or tasks that it stumbles on? Get to a point where you could kinda, like, look at a problem, and you can just tell, like, if the system's gonna get this one or not. And so, okay, once you're to that point, and it's like it's, like, it's kinda like an intuition thing. Once you're to that point, then you try to, like, poke it, and you kinda try to test at it. And you're like, which what what do I think might not work here? And you and you try to prove out, like, okay. Can I find some examples that indicate that this particular access of the system doesn't work? Let's say I in my vibe my end to end kind of IV valuing thing, I noticed, like, there are, you know, these kinds of questions, which it's both very fast on solving and questions sometimes solves them, but it's very slow. And it does not at all solve them consistently. It's kind of like a fifty fifty. Then I focus on those questions and I kind of look at them and I'm like, like, maybe it's because oh, our GREP tool like, doesn't ever use this particular kind of regex pattern. Or, like, our embedding models when we when the agent sends a query to it, like, is never asking this particular kind of question. And I think asking this particular kind of question or doing this kind of regex will really help. And then so you add that to your your tool schema. And maybe it helps. And then in terms of evals for the for those kind of, like, more pointed problems, you can you can do the Vibe Eval. Just so that embedding tool of that particular core of that particular task. And, usually, that's sufficient If you already have a quantitative eval lying around, that's that's nice. I will say once you get to a certain, like, quality bar with all these stuff of, like, different you know, following different intuitions for what's not working and what's working, You do eventually get to a point where you do need the quantitative eval, You know? So for Suitebench, that quantitative eval was it's 500 examples, which exists in this in in in Sweebench. And there's, like, a yes, no at the very end of if not the figured the problem out. Because you just look at if the test passed or not, You you do want that because as you're doing all these kind of, like, spot spot check improvements and even the vibe eval ing, like, one improvement may negate other improvements. You need to see how kind of everything that's So, eventually, you do need the quantitative eval once once you're kind of, have saturated all those those, like, early winds or all those low hanging fruit. Yeah. I wanna call out something that's really refreshing, which is when I talk to you and in the context of RAG, you're always giving very specific examples of queries that we want our system to service. I find that when we're talking to a lot of sort of really smart engineers but folks who are not as familiar with research, they sort of lead with questions like which embedding that model should I use? Should I use Hide, or should I use Agentic Rag versus your example of just, you know, what are these specific kinds of examples? What are the 10 you know, 20 examples that we can service What are the failure modes? And just becoming really familiar with the evaluation suite rather than the set of tools at our disposal. I just wanna call that out because that's not something that's, like, you know, very big in sort of a lot of the consulting work that I do. Always starts with, what tool should we use? And then it gets to we need to be able to handle contact search and scheduling much better. And we need to build specific tools to do that. Yeah. I I I'd say one of my controversial takes here is that being a researcher is actually very similar to being a product person. And okay. So what do I mean by this? Well, with both of them, you're working back from use cases. You're working backwards from examples. So most AI research these days is not solving a math question on a whiteboard. It's like data. It's like looking at, like, this particular agent run or this particular query and cross referencing it against other examples and trying to understand, like, why that query failed or not. So when you're analyzing this kind of use case level user user data or or exam you know, data of your your kind of task distribution. That's very similar to being a product person. And, like, looking at and studying how are people using my product and how can I improve it? And so I think there's actually a ton of overlap between product and and research, and engineering is, like, the skill that's kind of, like, the the odd man out. Yeah. That's that's that's pretty interesting. I'm also surprised because I think you're one of the few folks working on, like, autonomous coding that is still sort of long embedding models. What do you think is missing from these other folks? I think, for example, Klein has talked about, you know, not using embedding models in the past and going only for tool use without embeddings. Do you think this is a matter of, like, there are no good open source code embedding models out there? Is it because we're not training things correctly? Is it because you know, SONNET four is just too good at using grep that we don't need that the near term. Yeah. I mean, for for SWE bench, they're pretty small, they're pretty small repos. So GREP and FINE work great and fine. And so that's how we didn't need to use embedding models for them. Let's go again back to this guy. But, like, if you wanna get to, like, truly huge code bases, or you wanna be able to, like, search over a code base, and search over, like, low structure natural language that's, in Slack or in Notion, like, GREP and FIND are, like, less useful. And so maybe it's like you have, like, Glean as as one thing I mentioned or you for as your tool for searching over, like, Slack or whatever, or maybe you train your own embedding but it's not really just GREP and FIND. And then, you know, let's say we wanna search over third party code bases. Because these models don't they memorize a good amount of third party code bases, so they do memorize you know, the OpenAI SDK, but that, like, random SDK that you found, on GitHub that has, like, barely any stars that you're using, like, it probably hasn't memorized that. So you wanna be able to search over that. And maybe it has docs and if it has docs, like, yes. Awesome. Maybe it doesn't have docs. You actually wanna search over the code base. So the amount the corpus that you the, you know, size of the index can get really large, And in this situation, rapid find can get slow. And embedding models are really good at moving and moving quickly through these giant corpuses. So that's kind of the main thing I think about is, like, it's like how much do you wanna scale your index? And then two, like, what are the different kinds of information that you have in that index? And, like, GREP and FINE are, like, going to be less and less efficient. Oh, I'll give you another thing that I think we're going to have coding agents search over soon that GraphicFinder definitely not gonna work for. Searching over like, video recordings of user sessions. So, like, I have this maybe giant corpus of, like, you know, say I did a bunch of, like, customer studies. I I recorded them all, so I have all of these hours of video footage. And I wanna search over those when doing an agent call. Like, maybe I asked the agent, like, a product y question, or maybe I asked the agent, like, I'm thinking of moving this particular button on this web page from this location to this location. I probably need to search over all these videos. Obviously, GREP and find aren't going to be sufficient there. So it's really, like, about, like, again about your use case and asking yourself, like, okay. If I was a human working this use case and I was, a really persistent human that never got tired, would this having this other tool, like, this other search tool, would that help me? And if the answer is yes, then, like, it's probably gonna be useful for the agent. That makes sense. You feel like it's almost an artifact of SuiteBench being just, like, too much of a toy set of evals. Like, you know, I I think about, you know, my time at Facebook, where you just can't even grab the code base. We have, like, custom tools to get over our code base to understand, like, when were the like, who else called this function in the past, like, ninety days? And there's, a special UI because it's, like, some mono repo, which is know, luckily, it's a monorepo, but in other cases, that might be distributed systems and you can't grab any of that. Yeah. I I think I think that's that's spot on. SWE bench is, like, one of the most real world of the academic evals, and it's very good. But at the end of the day, it still is an academic eval. So the repos are all open source. Already biases them to be pretty small and makes them look kind of different from, like, professional repositories. And the tasks are pretty simple tasks. So there's actually so so there's Suitebench, and then there's Suitebench verified, which is what we use. So Suitebench is, like, there's many more examples. A lot of them kinda crappy, like low quality. Suitebench verified is, like, filtered down version that OpenAI released, but they just filter down to, like, 500 examples from Suitebench. So that's what what we worked with, and that's kind of the more useful one. SuiteBench verified, there there's some statistic, like, think it's 90% of the examples took less than an hour for, like, a good engineer to solve. So they're pretty simple examples. And so that simplicity also makes something like GREP and FIND a lot easier. And, again, as I mentioned, there's, like, there's still the the latency factor. Like, I don't know about you guys, but, like, I've definitely had these moments where my thumbs are twiddling for, like, five minutes because I'm waiting for an agent to run. I'm, like, that's super annoying. And, like, right now, we're, woah. These tools are so good. They're saving us so much time. Don't care about that. But, like, let's roll things forward. Like, and everyone's used to, like, gets used to the speed at which we're building with agents. Like, that's that that kind of latency is gonna get annoying. Yeah. It's like, the first time I get Wi Fi on a plane, I'm amazed. And the second time I'm I get Wi Fi on a plane, it's, like, too slow for me, and I'm already complaining. I definitely imagine that is gonna be the case. So there's about twelve minutes left. We try to end at fifty five because we have another talk in the hour. One question I'd like to ask everyone is what is the question you think people are not asking these days? And they really should. That's a really interesting topic. Seeing tokens come up. I I still you know, I don't know. I still come back to this point that I think no one very few people still under very few people understand how to build and train good retrieval systems. Like, it's like a simple thing, but it it just, like, confuses me like, why no one knows how to fine tune really good embedding models. So I'm gonna go with that one still. Wow. Is I know at least at Augment, like, we found having fine tuned embedding models was really help is really helpful net of GREP and FINE. So as you mentioned, like, other people say differently and that makes me wonder. Like, are they they know how to train embedding models really well? Yeah. Yeah. So I'm gonna go back to that that simple question. Yeah. And I and I bet a lot of it really is the fact that you know, what data do you have access to that allows you to train this embedding model? Like, the cursor team has been deploying this tab model for a year, Right? And so what they can train on is gonna be much larger volume than say, a smaller company building out, like, the cutting SDK. I can imagine codecs can probably get a really good code embedding model out pretty quickly But I don't I don't know if cursor sorry. Not cursor. Cloud Code can't because they don't really do any indexing right now. So, like, how how would you go back in time to build out that dataset? Yes. Training embedding models on user data is a very useful axis. This is correct. Yeah. I I think that's gonna be the the big factor, especially because also identifying whether or not the retrieval was good, I think it's gonna be very Come on in. Hey, Andrew. You're I'm muted. We'll cut that out. Don't worry. Yeah. I think, you know, one of the things I I just saw recently was, like, Cursor has their, like, bug bot. Right, where you make a request. It is on GitHub. There's a pull request, and maybe it's just, okay. Retrieval is good, but the pull request gets merged. But that's a ton of infrastructure to have to spin up to try to collect that data. I'm curious, like, do you sort of agree with that sentiment, or do you think it's actually something else entirely? Like, maybe it's not just we don't have the data, but, really, there's something fundamental about training these embedding models. Wait. So your question is eventually is is, like, essentially, like, does retrieval get harder for this is it retrieval hard for this new More like PR agents. Have people not trained these embedding models because they don't have the data and they don't have sort of sensors out in the world, like merge pull request to understand what is good retrieval, do you think there's something fundamental about like, the training of embedding models themselves rather than the datasets that they're they're coming from. I just think there aren't that many people that are good at this skill. Mhmm. Because it's, like, just niche enough it's, like, aren't a ton of people. And so, yeah, I think I think that's the main reason. Then I think the data is the next thing with code. There's a lot of data out there that you can wrangle in all different kinds of ways. From GitHub, and you can do all these transformations on it and stuff. Most most domains don't have that. So for code, it's mostly finding great people. Think for other domains, it's probably it's probably more the data. I'm also I wonder if just that the smartest people want to work in the LLM. And not on the event. No. That's actually an issue. That that that is, like, definitely an issue, like, more like, a macro level. Say also one interesting point that I was just thinking about when you mentioned bug bot, is, like, some other, like, code search tools that are really useful is, like, maybe I don't wanna just search over the code, but I don't wanna search over, like, a par par Git commits. I basically wanna see blame. Of, like, who edited what lie. Mhmm. Another thing is maybe I wanna do, like, hierarchical retrieval. So, like, asynchronously, not while someone's asking the agent a question, I wanna take, like, every file in the code base and, like, write, like, a one line summary of And I wanna take, like, every directory and write, like, a one line summary of that. Like, these kinds of things can have, like, a huge huge impact on, like, the performance of retrieval systems And so yeah. I don't know. I just think there's, like, so much there's so much to build here. And yeah, it's it's exciting. Yeah. I think you can also imagine giving, you know, these systems access to, like, the language servers or giving them access to the ability to sort of more naturally browse code rather than just parsing things through strings. You know? Like, I imagine, like, when I debug things, I kinda look at the stack trace. I look at the files, then I sort of traverse through functions. Maybe that traversal is one way of doing it. And then you just say, well, any sort of straight path to debugging an issue the embedding should be similar. Totally. I mean, one one thing with, like, language server protocols, which if anyone's not familiar with that is, that's essentially, like, when you're coding in your code base, see, like, little squigglies under, like, some part of your code that indicates some kind of error, like, that's this thing called the language protocol that's like, analyzing your code base at all times and, like, looking at the code and seeing if it's correct and, like, attaching, like, links between, like, a function name and, like, the function definition and so forth. Like, these are pretty slow. They sometimes don't work. Like, they don't work in broken code. They sometimes work in broken code, but, like, they don't always work. And before agents, it was a bit of a pain to, like, figure out how to, like, take LSPs with all the, like, issues. And challenges I have. Like, integrate into the code base in a way that into a retrieval system that, like, works really consistently. But with agents now, it's like, well, I'm just gonna, like, paint a very simple picture. You could have, an LSP LSP tool. And then if, like, that doesn't work, then the agent can just call the, like, code search tool or can call the bash tool. And so now we can kind of, like, more easily synthesize, like, different retrieval tools that have different pros and cons without having to, like, deal with a bunch complicated work and fine tuning re rank and stuff like that. Yeah. Think that's super relevant. I you're you're kinda like the first person outside of Oman who cursor talking about embedding models and then doing this kind of retrieval. It kinda gets me psyched to maybe take a look at this a little bit deeper myself. Yeah. For sure. Can I can I say, like, one more, like, fun Of course? Retrieval anecdote? So I'm working on, like, a a side project right now. Just like kind of like a weekend hacking project where I'm, like, building a a like, I'm trying to have, like, the world's largest index of, like, songs that you can retrieve over. So if I wanna, like, ask any int question about music ever, I have this, like, giant index of music. And I am I found, like, with ChatGPT, if I ask it a question, like, to I'm searching for a song, and I wanna find a song that's, like, like a mid nineties vibe female singer, insert other qualities. It's, like, not very good at doing this because it's like it has access to a web search tool and, like, web search is, like, not the right abstraction for doing this kind of search. So what I've done instead is I've taken every one of those songs, I've used an LLM to go do deep research on each song to come up with, like, a a dis a, like, dis like, a dossier about that song. And I also have reached on I have this description, and then now I'm doing search over that. And that works really well. I thought this was, like, a really cool example of, like, just one simple little, like, asynchronous preprocessing step. Like, took, like, a totally not working search system and turned it into something that, like, works really well. That makes a lot of sense. You're just kind creating these, like, synthetic summarization or summary embedding to use and then embedding in that. Yeah. I've I've seen that work on, like, movie scenes. I've seen that work on, like, websites, hierarchical, Yeah. I'm definitely gonna explore a little bit more about this. Yeah. And, you know, I think one thing I'll just call out is right after this talk, we have another talk coming up. From the VP of Algolia, and he's gonna basically talk about, like, query expansion, summarization, changing different embedding models, and how to learn from feedback. So I think that'll be a pretty interesting segue into some of the stuff you're talking about. Awesome. Excited. As always, we'll send an email with any of the references that you know as well as any kind of slides and GitHub repos. Before we go, Colin, is there anything else you wanted to to say? Where can we find you? Yeah. You can we'll we'll I'll have you send out my contact info, and people can message me. Perfect. Yeah. I think the feedback has that this has been super dense. People are really excited even just watch this back and get the slides. And so on behalf of everyone, thank you so much for sharing, your your expert knowledge. I feel like there's been very few people who have been really talking about kind of stuff. So this is a super for me. Alright. Awesome. Bye. 

---
Your AI Product Needs Evals
LLMs
evals
How to construct domain-specific LLM evaluation systems.
Author
Hamel Husain

Published
March 29, 2024

Table Of Contents
Motivation
Iterating Quickly == Success
Case Study: Lucy, A Real Estate AI Assistant
Problem: How To Systematically Improve The AI?
The Types Of Evaluation
Level 1: Unit Tests
Step 1: Write Scoped Tests
Step 2: Create Test Cases
Step 3: Run & Track Your Tests Regularly
Level 2: Human & Model Eval
Logging Traces
Looking At Your Traces
Automated Evaluation w/ LLMs
Level 3: A/B Testing
Evaluating RAG
Eval Systems Unlock Superpowers For Free
Fine-Tuning
Data Synthesis & Curation
Debugging
Conclusion
Motivation
I started working with language models five years ago when I led the team that created CodeSearchNet, a precursor to GitHub CoPilot. Since then, I’ve seen many successful and unsuccessful approaches to building LLM products. I’ve found that unsuccessful products almost always share a common root cause: a failure to create robust evaluation systems.

I’m currently an independent consultant who helps companies build domain-specific AI products. I hope companies can save thousands of dollars in consulting fees by reading this post carefully. As much as I love making money, I hate seeing folks make the same mistake repeatedly.

This post outlines my thoughts on building evaluation systems for LLMs-powered AI products.

Iterating Quickly == Success
Like software engineering, success with AI hinges on how fast you can iterate. You must have processes and tools for:

Evaluating quality (ex: tests).
Debugging issues (ex: logging & inspecting data).
Changing the behavior or the system (prompt eng, fine-tuning, writing code)
Many people focus exclusively on #3 above, which prevents them from improving their LLM products beyond a demo.1 Doing all three activities well creates a virtuous cycle differentiating great from mediocre AI products (see the diagram below for a visualization of this cycle).

If you streamline your evaluation process, all other activities become easy. This is very similar to how tests in software engineering pay massive dividends in the long term despite requiring up-front investment.

To ground this post in a real-world situation, I’ll walk through a case study in which we built a system for rapid improvement. I’ll primarily focus on evaluation as that is the most critical component.

Case Study: Lucy, A Real Estate AI Assistant
Rechat is a SaaS application that allows real estate professionals to perform various tasks, such as managing contracts, searching for listings, building creative assets, managing appointments, and more. The thesis of Rechat is that you can do everything in one place rather than having to context switch between many different tools.

Rechat’s AI assistant, Lucy, is a canonical AI product: a conversational interface that obviates the need to click, type, and navigate the software. During Lucy’s beginning stages, rapid progress was made with prompt engineering. However, as Lucy’s surface area expanded, the performance of the AI plateaued. Symptoms of this were:

Addressing one failure mode led to the emergence of others, resembling a game of whack-a-mole.
There was limited visibility into the AI system’s effectiveness across tasks beyond vibe checks.
Prompts expanded into long and unwieldy forms, attempting to cover numerous edge cases and examples.
Problem: How To Systematically Improve The AI?
To break through this plateau, we created a systematic approach to improving Lucy centered on evaluation. Our approach is illustrated by the diagram below.



This diagram is a best-faith effort to illustrate my mental model for improving AI systems. In reality, the process is non-linear and can take on many different forms that may or may not look like this diagram.

I discuss the various components of this system in the context of evaluation below.

The Types Of Evaluation
Rigorous and systematic evaluation is the most important part of the whole system. That is why “Eval and Curation” is highlighted in yellow at the center of the diagram. You should spend most of your time making your evaluation more robust and streamlined.

There are three levels of evaluation to consider:

Level 1: Unit Tests
Level 2: Model & Human Eval (this includes debugging)
Level 3: A/B testing
The cost of Level 3 > Level 2 > Level 1. This dictates the cadence and manner you execute them. For example, I often run Level 1 evals on every code change, Level 2 on a set cadence and Level 3 only after significant product changes. It’s also helpful to conquer a good portion of your Level 1 tests before you move into model-based tests, as they require more work and time to execute.

There isn’t a strict formula as to when to introduce each level of testing. You want to balance getting user feedback quickly, managing user perception, and the goals of your AI product. This isn’t too dissimilar from the balancing act you must do for products more generally.

Level 1: Unit Tests
Unit tests for LLMs are assertions (like you would write in pytest). Unlike typical unit tests, you want to organize these assertions for use in places beyond unit tests, such as data cleaning and automatic retries (using the assertion error to course-correct) during model inference. The important part is that these assertions should run fast and cheaply as you develop your application so that you can run them every time your code changes. If you have trouble thinking of assertions, you should critically examine your traces and failure modes. Also, do not shy away from using an LLM to help you brainstorm assertions!

Step 1: Write Scoped Tests
The most effective way to think about unit tests is to break down the scope of your LLM into features and scenarios. For example, one feature of Lucy is the ability to find real estate listings, which we can break down into scenarios like so:

Feature: Listing Finder

This feature to be tested is a function call that responds to a user request to find a real estate listing. For example, “Please find listings with more than 3 bedrooms less than $2M in San Jose, CA”

The LLM converts this into a query that gets run against the CRM. The assertion then verifies that the expected number of results is returned. In our test suite, we have three user inputs that trigger each of the scenarios below, which then execute corresponding assertions (this is an oversimplified example for illustrative purposes):

Scenario	Assertions
Only one listing matches user query	len(listing_array) == 1
Multiple listings match user query	len(listing_array) > 1
No listings match user query	len(listing_array) == 0

There are also generic tests that aren’t specific to any one feature. For example, here is the code for one such generic test that ensures the UUID is not mentioned in the output:

const noExposedUUID = message => {
  // Remove all text within double curly braces
  const sanitizedComment = message.comment.replace(/\{\{.*?\}\}/g, '')

  // Search for exposed UUIDs
  const regexp = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/ig
  const matches = Array.from(sanitizedComment.matchAll(regexp))
  expect(matches.length, 'Exposed UUIDs').to.equal(0, 'Exposed UUIDs found')
}

CRM results returned to the LLM contain fields that shouldn’t be surfaced to the user; such as the UUID associated with an entry. Our LLM prompt tells the LLM to not include UUIDs. We use a simple regex to assert that the LLM response doesn’t include UUIDs.

Rechat has hundreds of these unit tests. We continuously update them based on new failures we observe in the data as users challenge the AI or the product evolves. These unit tests are crucial to getting feedback quickly when iterating on your AI system (prompt engineering, improving RAG, etc.). Many people eventually outgrow their unit tests and move on to other levels of evaluation as their product matures, but it is essential not to skip this step!

Step 2: Create Test Cases
To test these assertions, you must generate test cases or inputs that will trigger all scenarios you wish to test. I often utilize an LLM to generate these inputs synthetically; for example, here is one such prompt Rechat uses to generate synthetic inputs for a feature that creates and retrieves contacts:

Write 50 different instructions that a real estate agent can give to his assistant to create contacts on his CRM. The contact details can include name, phone, email, partner name, birthday, tags, company, address and job.

For each of the instructions, you need to generate a second instruction which can be used to look up the created contact.

. The results should be a JSON code block with only one string as the instruction like the following:


[
  ["Create a contact for John (<EMAIL>)", 
  "What's the email address of John Smith?"]
]

Using the above prompt, we generate test cases like below:

[ 
    [
        'Create a contact for John Smith (<EMAIL>) with phone number ************ and address 123 Apple St.', 
        'What\'s the email address of John Smith?'
    ],
    [
        'Add Emily Johnson with phone ************, email <EMAIL>, and company ABC Inc.', 
        'What\'s the phone number for Emily Johnson?'
    ],
    [
        'Create a contact for Tom Williams with birthday 10/20/1985, company XYZ Ltd, and job title Manager.', 
        'What\'s Tom Williams\' job title?'
    ],
    [
        'Add a contact for Susan Brown with partner name James Brown, <NAME_EMAIL>.', 
    'What\'s the partner name of Susan Brown?'
    ],
…
]

For each of these test cases, we execute the first user input to create the contact. We then execute the second query to fetch that contact. If the CRM doesn’t return exactly 1 result then we know there was a problem either creating or fetching the contact. We can also run generic assertions like the one to verify UUIDs are not in the response. You must constantly update these tests as you observe data through human evaluation and debugging. The key is to make these as challenging as possible while representing users’ interactions with the system.

You don’t need to wait for production data to test your system. You can make educated guesses about how users will use your product and generate synthetic data. You can also let a small set of users use your product and let their usage refine your synthetic data generation strategy. One signal you are writing good tests and assertions is when the model struggles to pass them - these failure modes become problems you can solve with techniques like fine-tuning later on.

On a related note, unlike traditional unit tests, you don’t necessarily need a 100% pass rate. Your pass rate is a product decision, depending on the failures you are willing to tolerate.

Step 3: Run & Track Your Tests Regularly
There are many ways to orchestrate Level 1 tests. Rechat has been leveraging CI infrastructure (e.g., GitHub Actions, GitLab Pipelines, etc.) to execute these tests. However, the tooling for this part of the workflow is nascent and evolving rapidly.

My advice is to orchestrate tests that involve the least friction in your tech stack. In addition to tracking tests, you need to track the results of your tests over time so you can see if you are making progress. If you use CI, you should collect metrics along with versions of your tests/prompts outside your CI system for easy analysis and tracking.

I recommend starting simple and leveraging your existing analytics system to visualize your test results. For example, Rechat uses Metabase to track their LLM test results over time. Below is a screenshot of a dashboard Rechat built with Metabase:



This screenshot shows the prevalence of a particular error (shown in yellow) in Lucy before (left) vs after (right) we addressed it.

Level 2: Human & Model Eval
After you have built a solid foundation of Level 1 tests, you can move on to other forms of validation that cannot be tested by assertions alone. A prerequisite to performing human and model-based eval is to log your traces.

Logging Traces
A trace is a concept that has been around for a while in software engineering and is a log of a sequence of events such as user sessions or a request flow through a distributed system. In other words, tracing is a logical grouping of logs. In the context of LLMs, traces often refer to conversations you have with a LLM. For example, a user message, followed by an AI response, followed by another user message, would be an example of a trace.

There are a growing number of solutions for logging LLM traces.2 Rechat uses LangSmith, which logs traces and allows you to view them in a human-readable way with an interactive playground to iterate on prompts. Sometimes, logging your traces requires you to instrument your code. In this case, Rechat was using LangChain which automatically logs trace events to LangSmith for you. Here is a screenshot of what this looks like:



I like LangSmith - it doesn’t require that you use LangChain and is intuitive and easy to use. Searching, filtering, and reading traces are essential features for whatever solution you pick. I’ve found that some tools do not implement these basic functions correctly!

Looking At Your Traces
You must remove all friction from the process of looking at data. This means rendering your traces in domain-specific ways. I’ve often found that it’s better to build my own data viewing & labeling tool so I can gather all the information I need onto one screen. In Lucy’s case, we needed to look at many sources of information (trace log, the CRM, etc) to understand what the AI did. This is precisely the type of friction that needs to be eliminated. In Rechat’s case, this meant adding information like:

What tool (feature) & scenario was being evaluated.
Whether the trace resulted from a synthetic input or a real user input.
Filters to navigate between different tools and scenario combinations.
Links to the CRM and trace logging system for the current record.
I’ve built different variations of this tool for each problem I’ve worked on. Sometimes, I even need to embed another application to see what the user interaction looks like. Below is a screenshot of the tool we built to evaluate Rechat’s traces:



Another design choice specific to Lucy is that we noticed that many failures involved small mistakes in the final output of the LLM (format, content, etc). We decided to make the final output editable by a human so that we could curate & fix data for fine-tuning.

These tools can be built with lightweight front-end frameworks like Gradio, Streamlit, Panel, or Shiny in less than a day. The tool shown above was built with Shiny for Python. Furthermore, there are tools like Lilac which uses AI to search and filter data semantically, which is incredibly handy for finding a set of similar data points while debugging an issue.

I often start by labeling examples as good or bad. I’ve found that assigning scores or more granular ratings is more onerous to manage than binary ratings. There are advanced techniques you can use to make human evaluation more efficient or accurate (e.g., active learning, consensus voting, etc.), but I recommend starting with something simple. Finally, like unit tests, you should organize and analyze your human-eval results to assess if you are progressing over time.

As discussed later, these labeled examples measure the quality of your system, validate automated evaluation, and curate high-quality synthetic data for fine-tuning.

How much data should you look at?
I often get asked how much data to examine. When starting, you should examine as much data as possible. I usually read traces generated from ALL test cases and user-generated traces at a minimum. You can never stop looking at data—no free lunch exists. However, you can sample your data more over time, lessening the burden. 3

Automated Evaluation w/ LLMs
Many vendors want to sell you tools that claim to eliminate the need for a human to look at the data. Having humans periodically evaluate at least a sample of traces is a good idea. I often find that “correctness” is somewhat subjective, and you must align the model with a human.

You should track the correlation between model-based and human evaluation to decide how much you can rely on automatic evaluation. Furthermore, by collecting critiques from labelers explaining why they are making a decision, you can iterate on the evaluator model to align it with humans through prompt engineering or fine-tuning. However, I tend to favor prompt engineering for evaluator model alignment.

I love using low-tech solutions like Excel to iterate on aligning model-based eval with humans. For example, I sent my colleague Phillip the following spreadsheet every few days to grade for a different use-case involving a natural language query generator. This spreadsheet would contain the following information:

model response: this is the prediction made by the LLM.
model critique: this is a critique written by a (usually more powerful) LLM about your original LLM’s prediction.
model outcome: this is a binary label the critique model assigns to the model response as being “good” or “bad.”
Phillip then fills out his version of the same information - meaning his critique, outcome, and desired response for 25-50 examples at a time (these are the columns prefixed with “phillip_” below):



This information allowed me to iterate on the prompt of the critique model to make it sufficiently aligned with Phillip over time. This is also easy to track in a low-tech way in a spreadsheet:



This is a screenshot of a spreadsheet where we recorded our attempts to align model-based eval with a human evaluator.

General tips on model-based eval:

Use the most powerful model you can afford. It often takes advanced reasoning capabilities to critique something well. You can often get away with a slower, more powerful model for critiquing outputs relative to what you use in production.
Model-based evaluation is a meta-problem within your larger problem. You must maintain a mini-evaluation system to track its quality. I have sometimes fine-tuned a model at this stage (but I try not to).
After bringing the model-based evaluator in line with the human, you must continue doing periodic exercises to monitor the model and human agreement.
My favorite aspect about creating a good evaluator model is that its critiques can be used to curate high-quality synthetic data, which I will touch upon later.

Level 3: A/B Testing
Finally, it is always good to perform A/B tests to ensure your AI product is driving user behaviors or outcomes you desire. A/B testing for LLMs compared to other types of products isn’t too different. If you want to learn more about A/B testing, I recommend reading the Eppo blog (which was created by colleagues I used to work with who are rock stars in A/B testing).

It’s okay to put this stage off until you are sufficiently ready and convinced that your AI product is suitable for showing to real users. This level of evaluation is usually only appropriate for more mature products.

Evaluating RAG
Aside from evaluating your system as a whole, you can evaluate sub-components of your AI, like RAG. Evaluating RAG is beyond the scope of this post, but you can learn more about this subject in a post by Jason Liu.

Eval Systems Unlock Superpowers For Free
In addition to iterating fast, eval systems unlock the ability to fine-tune and debug, which can take your AI product to the next level.

Fine-Tuning
Rechat resolved many failure modes through fine-tuning that were not possible with prompt engineering alone. Fine-tuning is best for learning syntax, style, and rules, whereas techniques like RAG supply the model with context or up-to-date facts.

99% of the labor involved with fine-tuning is assembling high-quality data that covers your AI product’s surface area. However, if you have a solid evaluation system like Rechat’s, you already have a robust data generation and curation engine! I will expand more on the process of fine-tuning in a future post.4

Data Synthesis & Curation
To illustrate why data curation and synthesis come nearly for free once you have an evaluation system, consider the case where you want to create additional fine-tuning data for the listing finder mentioned earlier. First, you can use LLMs to generate synthetic data with a prompt like this:

Imagine if Zillow was able to parse natural language. Come up with 50 different ways users would be able to search listings there. Use real names for cities and neighborhoods.

You can use the following parameters:

<ommitted for confidentiality>

Output should be a JSON code block array. Example:

[
"Homes under $500k in New York"
]
This is almost identical to the exercise for producing test cases! You can then use your Level 1 & Level 2 tests to filter out undesirable data that fails assertions or that the critique model thinks are wrong. You can also use your existing human evaluation tools to look at traces to curate traces for a fine-tuning dataset.

Debugging
When you get a complaint or see an error related to your AI product, you should be able to debug this quickly. If you have a robust evaluation system, you already have:

A database of traces that you can search and filter.
A set of mechanisms (assertions, tests, etc) that can help you flag errors and bad behaviors.
Log searching & navigation tools that can help you find the root cause of the error. For example, the error could be RAG, a bug in the code, or a model performing poorly.
The ability to make changes in response to the error and quickly test its efficacy.
In short, there is an incredibly large overlap between the infrastructure needed for evaluation and that for debugging.

Conclusion
Evaluation systems create a flywheel that allows you to iterate very quickly. It’s almost always where people get stuck when building AI products. I hope this post gives you an intuition on how to go about building your evaluation systems. Some key takeaways to keep in mind:

Remove ALL friction from looking at data.
Keep it simple. Don’t buy fancy LLM tools. Use what you have first.
You are doing it wrong if you aren’t looking at lots of data.
Don’t rely on generic evaluation frameworks to measure the quality of your AI. Instead, create an evaluation system specific to your problem.
Write lots of tests and frequently update them.
LLMs can be used to unblock the creation of an eval system. Examples include using a LLM to:
Generate test cases and write assertions
Generate synthetic data
Critique and label data etc.
Re-use your eval infrastructure for debugging and fine-tuning.
I’d love to hear from you if you found this post helpful or have any questions. My <NAME_EMAIL>.

---
Frequently Asked Questions (And Answers) About AI Evals
LLMs
evals
FAQ from our course on AI Evals.
Authors
Hamel Husain

Shreya Shankar

Published
July 7, 2025

This document curates the most common questions Shreya and I received while teaching 700+ engineers & PMs AI Evals. Warning: These are sharp opinions about what works in most cases. They are not universal truths. Use your judgment.

👉 We are teaching our last and final cohort of our AI Evals course next month (we have to get back to building). Here is a 35% discount code for readers. 👈

Q: What are LLM Evals?
If you are completely new to product-specific LLM evals (not foundation model benchmarks), see these posts: part 1, part 2, part 3. Otherwise, keep reading.

Q: Is RAG dead?
Question: Should I avoid using RAG for my AI application after reading that “RAG is dead” for coding agents?

Many developers are confused about when and how to use RAG after reading articles claiming “RAG is dead.” Understanding what RAG actually means versus the narrow marketing definitions will help you make better architectural decisions for your AI applications.

The viral article claiming RAG is dead specifically argues against using naive vector database retrieval for autonomous coding agents, not RAG as a whole. This is a crucial distinction that many developers miss due to misleading marketing.

RAG simply means Retrieval-Augmented Generation - using retrieval to provide relevant context that improves your model’s output. The core principle remains essential: your LLM needs the right context to generate accurate answers. The question isn’t whether to use retrieval, but how to retrieve effectively.

For coding applications, naive vector similarity search often fails because code relationships are complex and contextual. Instead of abandoning retrieval entirely, modern coding assistants like Claude Code still uses retrieval —they just employ agentic search instead of relying solely on vector databases, similar to how human developers work.

You have multiple retrieval strategies available, ranging from simple keyword matching to embedding similarity to LLM-powered relevance filtering. The optimal approach depends on your specific use case, data characteristics, and performance requirements. Many production systems combine multiple strategies or use multi-hop retrieval guided by LLM agents.

Unfortunately, “RAG” has become a buzzword with no shared definition. Some people use it to mean any retrieval system, others restrict it to vector databases. Focus on the ultimate goal: getting your LLM the context it needs to succeed. Whether that’s through vector search, agentic exploration, or hybrid approaches is a product and engineering decision.

Rather than following categorical advice to avoid or embrace RAG, experiment with different retrieval approaches and measure what works best for your application.

Q: Can I use the same model for both the main task and evaluation?
For LLM-as-Judge selection, using the same model is usually fine because the judge is doing a different task than your main LLM pipeline. The judges we recommend building do scoped binary classification tasks. Focus on achieving high True Positive Rate (TPR) and True Negative Rate (TNR) with your judge on a held out labeled test set rather than avoiding the same model family. You can use these metrics on the test set to understand how well your judge is doing.

When selecting judge models, start with the most capable models available to establish strong alignment with human judgments. You can optimize for cost later once you’ve established reliable evaluation criteria. We do not recommend using the same model for open ended preferences or response quality (but we don’t recommend building judges this way in the first place!).

Q: How much time should I spend on model selection?
Many developers fixate on model selection as the primary way to improve their LLM applications. Start with error analysis to understand your failure modes before considering model switching. As Hamel noted in office hours, “I suggest not thinking of switching model as the main axes of how to improve your system off the bat without evidence. Does error analysis suggest that your model is the problem?”

Q: Should I build a custom annotation tool or use something off-the-shelf?
Build a custom annotation tool. This is the single most impactful investment you can make for your AI evaluation workflow. With AI-assisted development tools like Cursor or Lovable, you can build a tailored interface in hours. I often find that teams with custom annotation tools iterate ~10x faster.

Custom tools excel because:

They show all your context from multiple systems in one place
They can render your data in a product specific way (images, widgets, markdown, buttons, etc.)
They’re designed for your specific workflow (custom filters, sorting, progress bars, etc.)
Off-the-shelf tools may be justified when you need to coordinate dozens of distributed annotators with enterprise access controls. Even then, many teams find the configuration overhead and limitations aren’t worth it.

Isaac’s Anki flashcard annotation app shows the power of custom tools—handling 400+ results per query with keyboard navigation and domain-specific evaluation criteria that would be nearly impossible to configure in a generic tool.

Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings (Likert scales)?
Engineers often believe that Likert scales (1-5 ratings) provide more information than binary evaluations, allowing them to track gradual improvements. However, this added complexity often creates more problems than it solves in practice.

Binary evaluations force clearer thinking and more consistent labeling. Likert scales introduce significant challenges: the difference between adjacent points (like 3 vs 4) is subjective and inconsistent across annotators, detecting statistical differences requires larger sample sizes, and annotators often default to middle values to avoid making hard decisions.

Having binary options forces people to make a decision rather than hiding uncertainty in middle values. Binary decisions are also faster to make during error analysis - you don’t waste time debating whether something is a 3 or 4.

For tracking gradual improvements, consider measuring specific sub-components with their own binary checks rather than using a scale. For example, instead of rating factual accuracy 1-5, you could track “4 out of 5 expected facts included” as separate binary checks. This preserves the ability to measure progress while maintaining clear, objective criteria.

Start with binary labels to understand what ‘bad’ looks like. Numeric labels are advanced and usually not necessary.

Q: How do I debug multi-turn conversation traces?
Start simple. Check if the whole conversation met the user’s goal with a pass/fail judgment. Look at the entire trace and focus on the first upstream failure. Read the user-visible parts first to understand if something went wrong. Only then dig into the technical details like tool calls and intermediate steps.

When you find a failure, reproduce it with the simplest possible test case. Here’s an example: suppose a shopping bot gives the wrong return policy on turn 4 of a conversation. Before diving into the full multi-turn complexity, simplify it to a single turn: “What is the return window for product X1000?” If it still fails, you’ve proven the error isn’t about conversation context - it’s likely a basic retrieval or knowledge issue you can debug more easily.

For generating test cases, you have two main approaches. First, you can simulate users with another LLM to create realistic multi-turn conversations. Second, use “N-1 testing” where you provide the first N-1 turns of a real conversation and test what happens next. The N-1 approach often works better since it uses actual conversation prefixes rather than fully synthetic interactions (but is less flexible and doesn’t test the full conversation). User simulation is getting better as models improve. Keep an eye on this space.

The key is balancing thoroughness with efficiency. Not every multi-turn failure requires multi-turn analysis.

Q: Should I build automated evaluators for every failure mode I find?
Focus automated evaluators on failures that persist after fixing your prompts. Many teams discover their LLM doesn’t meet preferences they never actually specified - like wanting short responses, specific formatting, or step-by-step reasoning. Fix these obvious gaps first before building complex evaluation infrastructure.

Consider the cost hierarchy of different evaluator types. Simple assertions and reference-based checks (comparing against known correct answers) are cheap to build and maintain. LLM-as-Judge evaluators require 100+ labeled examples, ongoing weekly maintenance, and coordination between developers, PMs, and domain experts. This cost difference should shape your evaluation strategy.

Only build expensive evaluators for problems you’ll iterate on repeatedly. Since LLM-as-Judge comes with significant overhead, save it for persistent generalization failures - not issues you can fix trivially. Start with cheap code-based checks where possible: regex patterns, structural validation, or execution tests. Reserve complex evaluation for subjective qualities that can’t be captured by simple rules.

Q: How many people should annotate my LLM outputs?
For most small to medium-sized companies, appointing a single domain expert as a “benevolent dictator” is the most effective approach. This person—whether it’s a psychologist for a mental health chatbot, a lawyer for legal document analysis, or a customer service director for support automation—becomes the definitive voice on quality standards.

A single expert eliminates annotation conflicts and prevents the paralysis that comes from “too many cooks in the kitchen”. The benevolent dictator can incorporate input and feedback from others, but they drive the process. If you feel like you need five subject matter experts to judge a single interaction, it’s a sign your product scope might be too broad.

However, larger organizations or those operating across multiple domains (like a multinational company with different cultural contexts) may need multiple annotators. When you do use multiple people, you’ll need to measure their agreement using metrics like Cohen’s Kappa, which accounts for agreement beyond chance. However, use your judgment. Even in larger companies, a single expert is often enough.

Start with a benevolent dictator whenever feasible. Only add complexity when your domain demands it.

Q: What gaps in eval tooling should I be prepared to fill myself?
Most eval tools handle the basics well: logging complete traces, tracking metrics, prompt playgrounds, and annotation queues. These are table stakes. Here are four areas where you’ll likely need to supplement existing tools.

Watch for vendors addressing these gaps—it’s a strong signal they understand practitioner needs.

1. Error Analysis and Pattern Discovery
After reviewing traces where your AI fails, can your tooling automatically cluster similar issues? For instance, if multiple traces show the assistant using casual language for luxury clients, you need something that recognizes this broader “persona-tone mismatch” pattern. We recommend building capabilities that use AI to suggest groupings, rewrite your observations into clearer failure taxonomies, help find similar cases through semantic search, etc.

2. AI-Powered Assistance Throughout the Workflow
The most effective workflows use AI to accelerate every stage of evaluation. During error analysis, you want an LLM helping categorize your open-ended observations into coherent failure modes. For example, you might annotate several traces with notes like “wrong tone for investor,” “too casual for luxury buyer,” etc. Your tooling should recognize these as the same underlying pattern and suggest a unified “persona-tone mismatch” category.

You’ll also want AI assistance in proposing fixes. After identifying 20 cases where your assistant omits pet policies from property summaries, can your workflow analyze these failures and suggest specific prompt modifications? Can it draft refinements to your SQL generation instructions when it notices patterns of missing WHERE clauses?

Additionally, good workflows help you conduct data analysis of your annotations and traces. I like using notebooks with AI in-the-loop like Julius,Hex or SolveIt. These help me discover insights like “location ambiguity errors spike 3x when users mention neighborhood names” or “tone mismatches occur 80% more often in email generation than other modalities.”

3. Custom Evaluators Over Generic Metrics
Be prepared to build most of your evaluators from scratch. Generic metrics like “hallucination score” or “helpfulness rating” rarely capture what actually matters for your application—like proposing unavailable showing times or omitting budget constraints from emails. In our experience, successful teams spend most of their effort on application-specific metrics.

4. APIs That Support Custom Annotation Apps
Custom annotation interfaces work best for most teams. This requires observability platforms with thoughtful APIs. I often have to build my own libraries and abstractions just to make bulk data export manageable. You shouldn’t have to paginate through thousands of requests or handle timeout-prone endpoints just to get your data. Look for platforms that provide true bulk export capabilities and, crucially, APIs that let you write annotations back efficiently.

Q: What is the best approach for generating synthetic data?
A common mistake is prompting an LLM to "give me test queries" without structure, resulting in generic, repetitive outputs. A structured approach using dimensions produces far better synthetic data for testing LLM applications.

Start by defining dimensions: categories that describe different aspects of user queries. Each dimension captures one type of variation in user behavior. For example:

For a recipe app, dimensions might include Dietary Restriction (vegan, gluten-free, none), Cuisine Type (Italian, Asian, comfort food), and Query Complexity (simple request, multi-step, edge case).
For a customer support bot, dimensions could be Issue Type (billing, technical, general), Customer Mood (frustrated, neutral, happy), and Prior Context (new issue, follow-up, resolved).
Choose dimensions that target likely failure modes. If you suspect your recipe app struggles with scaling ingredients for large groups or your support bot mishandles angry customers, make those dimensions. Use your application first—you need hypotheses about where failures occur. Without this, you’ll generate useless test data.

Once you have dimensions, create tuples: specific combinations selecting one value from each dimension. A tuple like (Vegan, Italian, Multi-step) represents a particular use case. Write 20 tuples manually to understand your problem space, then use an LLM to scale up.

The two-step generation process is important. First, have the LLM generate structured tuples. Then, in a separate prompt, convert each tuple to a natural language query. This separation prevents repetitive phrasing. For the vegan Italian tuple above, you might get "I need a dairy-free lasagna recipe that I can prep the day before."

Don’t generate synthetic data for problems you can fix immediately. If your prompt never mentions handling dietary restrictions, fix the prompt rather than generating hundreds of specialized queries. Save synthetic data for complex issues requiring iteration—like an LLM consistently failing at ingredient scaling math or misinterpreting ambiguous requests.

After iterating on your tuples and prompts, run these synthetic queries through your actual system to capture full traces. Sample 100 traces for error analysis. This number provides enough traces to manually review and identify failure patterns without being overwhelming. Rather than generating thousands of similar queries, ensure your 100 traces cover diverse combinations across your dimensions—this variety will reveal more failure modes than sheer volume.

Q: How do I approach evaluation when my system handles diverse user queries?
Complex applications often support vastly different query patterns—from “What’s the return policy?” to “Compare pricing trends across regions for products matching these criteria.” Each query type exercises different system capabilities, leading to confusion on how to design eval criteria.

Error Analysis is all you need. Your evaluation strategy should emerge from observed failure patterns (e.g. error analysis), not predetermined query classifications. Rather than creating a massive evaluation matrix covering every query type you can imagine, let your system’s actual behavior guide where you invest evaluation effort.

During error analysis, you’ll likely discover that certain query categories share failure patterns. For instance, all queries requiring temporal reasoning might struggle regardless of whether they’re simple lookups or complex aggregations. Similarly, queries that need to combine information from multiple sources might fail in consistent ways. These patterns discovered through error analysis should drive your evaluation priorities. It could be that query category is a fine way to group failures, but you don’t know that until you’ve analyzed your data.

To see an example of basic error analysis in action, see this video.

👉 We are teaching our last and final cohort of our AI Evals course next month (we have to get back to building). Here is a 35% discount code for readers. 👈

Q: How do I choose the right chunk size for my document processing tasks?
Unlike RAG, where chunks are optimized for retrieval, document processing assumes the model will see every chunk. The goal is to split text so the model can reason effectively without being overwhelmed. Even if a document fits within the context window, it might be better to break it up. Long inputs can degrade performance due to attention bottlenecks, especially in the middle of the context. Two task types require different strategies:

1. Fixed-Output Tasks → Large Chunks
These are tasks where the output length doesn’t grow with input: extracting a number, answering a specific question, classifying a section. For example:

“What’s the penalty clause in this contract?”
“What was the CEO’s salary in 2023?”
Use the largest chunk (with caveats) that likely contains the answer. This reduces the number of queries and avoids context fragmentation. However, avoid adding irrelevant text. Models are sensitive to distraction, especially with large inputs. The middle parts of a long input might be under-attended. Furthermore, if cost and latency are a bottleneck, you should consider preprocessing or filtering the document (via keyword search or a lightweight retriever) to isolate relevant sections before feeding a huge chunk.

2. Expansive-Output Tasks → Smaller Chunks
These include summarization, exhaustive extraction, or any task where output grows with input. For example:

“Summarize each section”
“List all customer complaints”
In these cases, smaller chunks help preserve reasoning quality and output completeness. The standard approach is to process each chunk independently, then aggregate results (e.g., map-reduce). When sizing your chunks, try to respect content boundaries like paragraphs, sections, or chapters. Chunking also helps mitigate output limits. By breaking the task into pieces, each piece’s output can stay within limits.

General Guidance
It’s important to recognize why chunk size affects results. A larger chunk means the model has to reason over more information in one go – essentially, a heavier cognitive load. LLMs have limited capacity to retain and correlate details across a long text. If too much is packed in, the model might prioritize certain parts (commonly the beginning or end) and overlook or “forget” details in the middle. This can lead to overly coarse summaries or missed facts. In contrast, a smaller chunk bounds the problem: the model can pay full attention to that section. You are trading off global context for local focus.

No rule of thumb can perfectly determine the best chunk size for your use case – you should validate with experiments. The optimal chunk size can vary by domain and model. I treat chunk size as a hyperparameter to tune.

Q: How should I approach evaluating my RAG system?
RAG systems have two distinct components that require different evaluation approaches: retrieval and generation.

The retrieval component is a search problem. Evaluate it using traditional information retrieval (IR) metrics. Common examples include Recall@k (of all relevant documents, how many did you retrieve in the top k?), Precision@k (of the k documents retrieved, how many were relevant?), or MRR (how high up was the first relevant document?). The specific metrics you choose depend on your use case. These metrics are pure search metrics that measure whether you’re finding the right documents (more on this below).

To evaluate retrieval, create a dataset of queries paired with their relevant documents. Generate this synthetically by taking documents from your corpus, extracting key facts, then generating questions those facts would answer. This reverse process gives you query-document pairs for measuring retrieval performance without manual annotation.

For the generation component—how well the LLM uses retrieved context, whether it hallucinates, whether it answers the question—use the same evaluation procedures covered throughout this course: error analysis to identify failure modes, collecting human labels, building LLM-as-judge evaluators, and validating those judges against human annotations.

Jason Liu’s “There Are Only 6 RAG Evals” provides a framework that maps well to this separation. His Tier 1 covers traditional IR metrics for retrieval. Tiers 2 and 3 evaluate relationships between Question, Context, and Answer—like whether the context is relevant (C|Q), whether the answer is faithful to context (A|C), and whether the answer addresses the question (A|Q).

In addition to Jason’s six evals, error analysis on your specific data may reveal domain-specific failure modes that warrant their own metrics. For example, a medical RAG system might consistently fail to distinguish between drug dosages for adults versus children, or a legal RAG might confuse jurisdictional boundaries. These patterns emerge only through systematic review of actual failures. Once identified, you can create targeted evaluators for these specific issues beyond the general framework.

Finally, when implementing Jason’s Tier 2 and 3 metrics, don’t just use prompts off the shelf. The standard LLM-as-judge process requires several steps: error analysis, prompt iteration, creating labeled examples, and measuring your judge’s accuracy against human labels. Once you know your judge’s True Positive and True Negative rates, you can correct its estimates to determine the actual failure rate in your system. Skip this validation and your judges may not reflect your actual quality criteria.

In summary, debug retrieval first using IR metrics, then tackle generation quality using properly validated LLM judges.

Q: What makes a good custom interface for reviewing LLM outputs?
Great interfaces make human review fast, clear, and motivating. We recommend building your own annotation tool customized to your domain. The following features are possible enhancements we’ve seen work well, but you don’t need all of them. The screenshots shown are illustrative examples to clarify concepts. In practice, I rarely implement all these features in a single app. It’s ultimately a judgment call based on your specific needs and constraints.

1. Render Traces Intelligently, Not Generically: Present the trace in a way that’s intuitive for the domain. If you’re evaluating generated emails, render them to look like emails. If the output is code, use syntax highlighting. Allow the reviewer to see the full trace (user input, tool calls, and LLM reasoning), but keep less important details in collapsed sections that can be expanded. Here is an example of a custom annotation tool for reviewing real estate assistant emails:



A custom interface for reviewing emails for a real estate assistant.
2. Show Progress and Support Keyboard Navigation: Keep reviewers in a state of flow by minimizing friction and motivating completion. Include progress indicators (e.g., “Trace 45 of 100”) to keep the review session bounded and encourage completion. Enable hotkeys for navigating between traces (e.g., N for next), applying labels, and saving notes quickly. Below is an illustration of these features:



An annotation interface with a progress bar and hotkey guide
4. Trace navigation through clustering, filtering, and search: Allow reviewers to filter traces by metadata or search by keywords. Semantic search helps find conceptually similar problems. Clustering similar traces (like grouping by user persona) lets reviewers spot recurring issues and explore hypotheses. Below is an illustration of these features:



Cluster view showing groups of emails, such as property-focused or client-focused examples. Reviewers can drill into a group to see individual traces.
5. Prioritize labeling traces you think might be problematic: Surface traces flagged by guardrails, CI failures, or automated evaluators for review. Provide buttons to take actions like adding to datasets, filing bugs, or re-running pipeline tests. Display relevant context (pipeline version, eval scores, reviewer info) directly in the interface to minimize context switching. Below is an illustration of these ideas:



A trace view that allows you to quickly see auto-evaluator verdict, add traces to dataset or open issues. Also shows metadata like pipeline version, reviewer info, and more.
General Principle: Keep it minimal
Keep your annotation interface minimal. Only incorporate these ideas if they provide a benefit that outweighs the additional complexity and maintenance overhead.

Q: How much of my development budget should I allocate to evals?
It’s important to recognize that evaluation is part of the development process rather than a distinct line item, similar to how debugging is part of software development.

You should always be doing error analysis. When you discover issues through error analysis, many will be straightforward bugs you’ll fix immediately. These fixes don’t require separate evaluation infrastructure as they’re just part of development.

The decision to build automated evaluators comes down to cost-benefit analysis. If you can catch an error with a simple assertion or regex check, the cost is minimal and probably worth it. But if you need to align an LLM-as-judge evaluator, consider whether the failure mode warrants that investment.

In the projects we’ve worked on, we’ve spent 60-80% of our development time on error analysis and evaluation. Expect most of your effort to go toward understanding failures (i.e. looking at data) rather than building automated checks.

Be wary of optimizing for high eval pass rates. If you’re passing 100% of your evals, you’re likely not challenging your system enough. A 70% pass rate might indicate a more meaningful evaluation that’s actually stress-testing your application. Focus on evals that help you catch real issues, not ones that make your metrics look good.

Q: Why is “error analysis” so important in LLM evals, and how is it performed?
Error analysis is the most important activity in evals. Error analysis helps you decide what evals to write in the first place. It allows you to identify failure modes unique to your application and data. The process involves:

Creating a Dataset: Gathering representative traces of user interactions with the LLM. If you do not have any data, you can generate synthetic data to get started.

Open Coding: Human annotator(s) (ideally a benevolent dictator) review and write open-ended notes about traces, noting any issues. This process is akin to “journaling” and is adapted from qualitative research methodologies. When beginning, it is recommended to focus on noting the first failure observed in a trace, as upstream errors can cause downstream issues, though you can also tag all independent failures if feasible. A domain expert should be performing this step.

Axial Coding: Categorize the open-ended notes into a “failure taxonomy.”. In other words, group similar failures into distinct categories. This is the most important step. At the end, count the number of failures in each category. You can use a LLM to help with this step.

Iterative Refinement: Keep iterating on more traces until you reach theoretical saturation, meaning new traces do not seem to reveal new failure modes or information to you. As a rule of thumb, you should aim to review at least 100 traces.

You should frequently revisit this process. There are advanced ways to sample data more efficiently, like clustering, sorting by user feedback, and sorting by high probability failure patterns. Over time, you’ll develop a “nose” for where to look for failures in your data.

Do not skip error analysis. It ensures that the evaluation metrics you develop are supported by real application behaviors instead of counter-productive generic metrics (which most platforms nudge you to use). For examples of how error analysis can be helpful, see this video, or this blog post.

Q: What’s the difference between guardrails & evaluators?
Guardrails are inline safety checks that sit directly in the request/response path. They validate inputs or outputs before anything reaches a user, so they typically are:

Fast and deterministic – typically a few milliseconds of latency budget.
Simple and explainable – regexes, keyword block-lists, schema or type validators, lightweight classifiers.
Targeted at clear-cut, high-impact failures – PII leaks, profanity, disallowed instructions, SQL injection, malformed JSON, invalid code syntax, etc.
If a guardrail triggers, the system can redact, refuse, or regenerate the response. Because these checks are user-visible when they fire, false positives are treated as production bugs; teams version guardrail rules, log every trigger, and monitor rates to keep them conservative.

On the other hand, evaluators typically run after a response is produced. Evaluators measure qualities that simple rules cannot, such as factual correctness, completeness, etc. Their verdicts feed dashboards, regression tests, and model-improvement loops, but they do not block the original answer.

Evaluators are usually run asynchronously or in batch to afford heavier computation such as a LLM-as-a-Judge. Inline use of an LLM-as-Judge is possible only when the latency budget and reliability targets allow it. Slow LLM judges might be feasible in a cascade that runs on the minority of borderline cases.

Apply guardrails for immediate protection against objective failures requiring intervention. Use evaluators for monitoring and improving subjective or nuanced criteria. Together, they create layered protection.

Word of caution: Do not use llm guardrails off the shelf blindly. Always look at the prompt.

Q: What’s a minimum viable evaluation setup?
Start with error analysis, not infrastructure. Spend 30 minutes manually reviewing 20-50 LLM outputs whenever you make significant changes. Use one domain expert who understands your users as your quality decision maker (a “benevolent dictator”).

If possible, use notebooks to help you review traces and analyze data. In our opinion, this is the single most effective tool for evals because you can write arbitrary code, visualize data, and iterate quickly. You can even build your own custom annotation interface right inside notebooks, as shown in this video.

Q: How do I evaluate agentic workflows?
We recommend evaluating agentic workflows in two phases:

1. End-to-end task success. Treat the agent as a black box and ask “did we meet the user’s goal?”. Define a precise success rule per task (exact answer, correct side-effect, etc.) and measure with human or aligned LLM judges. Take note of the first upstream failure when conducting error analysis.

Once error analysis reveals which workflows fail most often, move to step-level diagnostics to understand why they’re failing.

2. Step-level diagnostics. Assuming that you have sufficiently instrumented your system with details of tool calls and responses, you can score individual components such as: - Tool choice: was the selected tool appropriate? - Parameter extraction: were inputs complete and well-formed? - Error handling: did the agent recover from empty results or API failures? - Context retention: did it preserve earlier constraints? - Efficiency: how many steps, seconds, and tokens were spent? - Goal checkpoints: for long workflows verify key milestones.

Example: “Find Berkeley homes under $1M and schedule viewings” breaks into: parameters extracted correctly, relevant listings retrieved, availability checked, and calendar invites sent. Each checkpoint can pass or fail independently, making debugging tractable.

Use transition failure matrices to understand error patterns. Create a matrix where rows represent the last successful state and columns represent where the first failure occurred. This is a great way to understand where the most failures occur.



Transition failure matrix showing hotspots in text-to-SQL agent workflow
Transition matrices transform overwhelming agent complexity into actionable insights. Instead of drowning in individual trace reviews, you can immediately see that GenSQL → ExecSQL transitions cause 12 failures while DecideTool → PlanCal causes only 2. This data-driven approach guides where to invest debugging effort. Here is another example from Bryan Bischof, that is also a text-to-SQL agent:



Bischof, Bryan “Failure is A Funnel - Data Council, 2025”
In this example, Bryan shows variation in transition matrices across experiments. How you organize your transition matrix depends on the specifics of your application. For example, Bryan’s text-to-SQL agent has an inherent sequential workflow which he exploits for further analytical insight. You can watch his full talk for more details.

Creating Test Cases for Agent Failures

Creating test cases for agent failures follows the same principles as our previous FAQ on debugging multi-turn conversation traces (i.e. try to reproduce the error in the simplest way possible, only use multi-turn tests when the failure actually requires conversation context, etc.).

Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor?
Eval tools are in an intensely competitive space. It would be futile to compare their features. If I tried to do such an analysis, it would be invalidated in a week! Vendors I encounter the most organically in my work are: Langsmith, Arize and Braintrust.

When I help clients with vendor selection, the decision weighs heavily towards who can offer the best support, as opposed to purely features. This changes depending on size of client, use case, etc. Yes - it’s mainly the human factor that matters, and dare I say, vibes.

I have no favorite vendor. At the core, their features are very similar - and I often build custom tools on top of them to fit my needs.

My suggestion is to explore the vendors and see which one you like the most.

Q: How are evaluations used differently in CI/CD vs. monitoring production?
The most important difference between CI vs. production evaluation is the data used for testing.

Test datasets for CI are small (in many cases 100+ examples) and purpose-built. Examples cover core features, regression tests for past bugs, and known edge cases. Since CI tests are run frequently, the cost of each test has to be carefully considered (that’s why you carefully curate the dataset). Favor assertions or other deterministic checks over LLM-as-judge evaluators.

For evaluating production traffic, you can sample live traces and run evaluators against them asynchronously. Since you usually lack reference outputs on production data, you might rely more on on more expensive reference-free evaluators like LLM-as-judge. Additionally, track confidence intervals for production metrics. If the lower bound crosses your threshold, investigate further.

These two systems are complementary: when production monitoring reveals new failure patterns through error analysis and evals, add representative examples to your CI dataset. This mitigates regressions on new issues.

Q: Are similarity metrics (BERTScore, ROUGE, etc.) useful for evaluating LLM outputs?
Generic metrics like BERTScore, ROUGE, cosine similarity, etc. are not useful for evaluating LLM outputs in most AI applications. Instead, we recommend using error analysis to identify metrics specific to your application’s behavior. We recommend designing binary pass/fail evals (using LLM-as-judge) or code-based assertions.

As an example, consider a real estate CRM assistant. Suggesting showings that aren’t available (can be tested with an assertion) or confusing client personas (can be tested with a LLM-as-judge) is problematic . Generic metrics like similarity or verbosity won’t catch this. A relevant quote from the course:

“The abuse of generic metrics is endemic. Many eval vendors promote off the shelf metrics, which ensnare engineers into superfluous tasks.”

Similarity metrics aren’t always useless. They have utility in domains like search and recommendation (and therefore can be useful for optimizing and debugging retrieval for RAG). For example, cosine similarity between embeddings can measure semantic closeness in retrieval systems, and average pairwise similarity can assess output diversity (where lower similarity indicates higher diversity).

Q: Should I use “ready-to-use” evaluation metrics?
No. Generic evaluations waste time and create false confidence. (Unless you’re using them for exploration).

One instructor noted:

“All you get from using these prefab evals is you don’t know what they actually do and in the best case they waste your time and in the worst case they create an illusion of confidence that is unjustified.”1

Generic evaluation metrics are everywhere. Eval libraries contain scores like helpfulness, coherence, quality, etc. promising easy evaluation. These metrics measure abstract qualities that may not matter for your use case. Good scores on them don’t mean your system works.

Instead, conduct error analysis to understand failures. Define binary failure modes based on real problems. Create custom evaluators for those failures and validate them against human judgment. Essentially, the entire evals process.

Experienced practitioners may still use these metrics, just not how you’d expect. As Picasso said: “Learn the rules like a pro, so you can break them like an artist.” Once you understand why generic metrics fail as evaluations, you can repurpose them as exploration tools to find interesting traces (explained in the next FAQ).

Q: How can I efficiently sample production traces for review?
It can be cubersome to review traces randomly, especially when most traces don’t have an error. These sampling strategies help you find traces more likely to reveal problems:

Outlier detection: Sort by any metric (response length, latency, tool calls) and review extremes.
User feedback signals: Prioritize traces with negative feedback, support tickets, or escalations.
Metric-based sorting: Generic metrics can serve as exploration signals to find interesting traces. Review both high and low scores and treat them as exploration clues. Based on what you learn, you can build custom evaluators for the failure modes you find.
Stratified sampling: Group traces by key dimensions (user type, feature, query category) and sample from each group.
---

Creating a LLM-as-a-Judge That Drives Business Results
LLMs
evals
A step-by-step guide with my learnings from 30+ AI implementations.
Author
Hamel Husain

Published
October 29, 2024

Table Of Contents
The Problem: AI Teams Are Drowning in Data
Step 1: Find The Principal Domain Expert
Next Steps
Step 2: Create a Dataset
Why a Diverse Dataset Matters
Dimensions for Structuring Your Dataset
Examples of Features, Scenarios, and Personas
This taxonomy is not universal
Generating Data
Example LLM Prompts for Generating User Inputs
Generating Synthetic Data
Next Steps
Step 3: Direct The Domain Expert to Make Pass/Fail Judgments with Critiques
Why are simple pass/fail metrics important?
The Role of Critiques
Examples of Good Critiques
Don’t stray from binary pass/fail judgments when starting out
Make it easy for the domain expert to review data
How many examples do you need?
Step 4: Fix Errors
Step 5: Build Your LLM as A Judge, Iteratively
The Hidden Power of Critiques
Start with Expert Examples
Keep Iterating on the Prompt Until Convergence With Domain Expert
How to Optimize the LLM Judge Prompt?
The Human Side of the Process
How Often Should You Evaluate?
What if this doesn’t work?
Mistakes I’ve noticed in LLM judge prompts
Step 6: Perform Error Analysis
Classify Traces
An Interactive Walkthrough of Error Analysis
Fix Your Errors, Again
Doing this well requires data literacy
Step 7: Create More Specialized LLM Judges (if needed)
Recap of Critique Shadowing
It’s Not The Judge That Created Value, Afterall
Do You Really Need This?
FAQ
If I have a good judge LLM, isn’t that also the LLM I’d also want to use?
Do you recommend fine-tuning judges?
What’s wrong with off-the-shelf LLM judges?
How Do you evaluate the LLM judge?
What model do you use for the LLM judge?
What about guardrails?
I’m using LLM as a judge, and getting tremendous value but I didn’t follow this approach.
How do you choose between traditional ML techniques, LLM-as-a-judge and human annotations?
Can you make judges from small models?
How do you ensure consistency when updating your LLM model?
How do you phase out human in the loop to scale this?
Resources
Stay Connected
Earlier this year, I wrote Your AI product needs evals. Many of you asked, “How do I get started with LLM-as-a-judge?” This guide shares what I’ve learned after helping over 30 companies set up their evaluation systems.

The Problem: AI Teams Are Drowning in Data
Ever spend weeks building an AI system, only to realize you have no idea if it’s actually working? You’re not alone. I’ve noticed teams repeat the same mistakes when using LLMs to evaluate AI outputs:

Too Many Metrics: Creating numerous measurements that become unmanageable.
Arbitrary Scoring Systems: Using uncalibrated scales (like 1-5) across multiple dimensions, where the difference between scores is unclear and subjective. What makes something a 3 versus a 4? Nobody knows, and different evaluators often interpret these scales differently.
Ignoring Domain Experts: Not involving the people who understand the subject matter deeply.
Unvalidated Metrics: Using measurements that don’t truly reflect what matters to the users or the business.
The result? Teams end up buried under mountains of metrics or data they don’t trust and can’t use. Progress grinds to a halt. Everyone gets frustrated.

For example, it’s not uncommon for me to see dashboards that look like this:



An illustrative example of a bad eval dashboard
Tracking a bunch of scores on a 1-5 scale is often a sign of a bad eval process (I’ll discuss why later). In this post, I’ll show you how to avoid these pitfalls. The solution is to use a technique that I call “Critique Shadowing”. Here’s how to do it, step by step.

Step 1: Find The Principal Domain Expert
In most organizations there is usually one (maybe two) key individuals whose judgment is crucial for the success of your AI product. These are the people with deep domain expertise or represent your target users. Identifying and involving this Principal Domain Expert early in the process is critical.

Why is finding the right domain expert so important?

They Set the Standard: This person not only defines what is acceptable technically, but also helps you understand if you’re building something users actually want.

Capture Unspoken Expectations: By involving them, you uncover their preferences and expectations, which they might not be able to fully articulate upfront. Through the evaluation process, you help them clarify what a “passable” AI interaction looks like.

Consistency in Judgment: People in your organization may have different opinions about the AI’s performance. Focusing on the principal expert ensures that evaluations are consistent and aligned with the most critical standards.

Sense of Ownership: Involving the expert gives them a stake in the AI’s development. They feel invested because they’ve had a hand in shaping it. In the end, they are more likely to approve of the AI.

Examples of Principal Domain Experts:

A psychologist for a mental health AI assistant.
A lawyer for an AI that analyzes legal documents.
A customer service director for a support chatbot.
A lead teacher or curriculum developer for an educational AI tool.
Exceptions
In a smaller company, this might be the CEO or founder. If you are an independent developer, you should be the domain expert (but be honest with yourself about your expertise).

If you must rely on leadership, you should regularly validate their assumptions against real user feedback.

Many developers attempt to act as the domain expert themselves, or find a convenient proxy (ex: their superior). This is a recipe for disaster. People will have varying opinions about what is acceptable, and you can’t make everyone happy. What’s important is that your principal domain expert is satisfied.

Remember: This doesn’t have to take a lot of the domain expert’s time. Later in this post, I’ll discuss how you can make the process efficient. Their involvement is absolutely critical to the AI’s success.

Next Steps
Once you’ve found your expert, we need to give them the right data to review. Let’s talk about how to do that next.

Step 2: Create a Dataset
With your principal domain expert on board, the next step is to build a dataset that captures problems that your AI will encounter. It’s important that the dataset is diverse and represents the types of interactions that your AI will have in production.

Why a Diverse Dataset Matters
Comprehensive Testing: Ensures your AI is evaluated across a wide range of situations.
Realistic Interactions: Reflects actual user behavior for more relevant evaluations.
Identifies Weaknesses: Helps uncover areas where the AI may struggle or produce errors.
Dimensions for Structuring Your Dataset
You want to define dimensions that make sense for your use case. For example, here are ones that I often use for B2C applications:

Features: Specific functionalities of your AI product.
Scenarios: Situations or problems the AI may encounter and needs to handle.
Personas: Representative user profiles with distinct characteristics and needs.
Examples of Features, Scenarios, and Personas
Features
Feature	Description
Email Summarization	Condensing lengthy emails into key points.
Meeting Scheduler	Automating the scheduling of meetings across time zones.
Order Tracking	Providing shipment status and delivery updates.
Contact Search	Finding and retrieving contact information from a database.
Language Translation	Translating text between languages.
Content Recommendation	Suggesting articles or products based on user interests.
Scenarios
Scenarios are situations the AI needs to handle, (not based on the outcome of the AI’s response).

Scenario	Description
Multiple Matches Found	User’s request yields multiple results that need narrowing down. For example: User asks “Where’s my order?” but has three active orders (#123, #124, #125). AI must help identify which specific order they’re asking about.
No Matches Found	User’s request yields no results, requiring alternatives or corrections. For example: User searches for order #ABC-123 which doesn’t exist. AI should explain valid order formats and suggest checking their confirmation email.
Ambiguous Request	User input lacks necessary specificity. For example: User says “I need to change my delivery” without specifying which order or what aspect of delivery (date, address, etc.) they want to change.
Invalid Data Provided	User provides incorrect data type or format. For example: User tries to track a return using a regular order number instead of a return authorization (RMA) number.
System Errors	Technical issues prevent normal operation. For example: While looking up an order, the inventory database is temporarily unavailable. AI needs to explain the situation and provide alternatives.
Incomplete Information	User omits required details. For example: User wants to initiate a return but hasn’t provided the order number or reason. AI needs to collect this information step by step.
Unsupported Feature	User requests functionality that doesn’t exist. For example: User asks to change payment method after order has shipped. AI must explain why this isn’t possible and suggest alternatives.
Personas
Persona	Description
New User	Unfamiliar with the system; requires guidance.
Expert User	Experienced; expects efficiency and advanced features.
Non-Native Speaker	May have language barriers; uses non-standard expressions.
Busy Professional	Values quick, concise responses; often multitasking.
Technophobe	Uncomfortable with technology; needs simple instructions.
Elderly User	May not be tech-savvy; requires patience and clear guidance.
This taxonomy is not universal
This taxonomy (features, scenarios, personas) is not universal. For example, it may not make sense to even have personas if users aren’t directly engaging with your AI. The idea is you should outline dimensions that make sense for your use case and generate data that covers them. You’ll likely refine these after the first round of evaluations.

Generating Data
To build your dataset, you can:

Use Existing Data: Sample real user interactions or behaviors from your AI system.
Generate Synthetic Data: Use LLMs to create realistic user inputs covering various features, scenarios, and personas.
Often, you’ll do a combination of both to ensure comprehensive coverage. Synthetic data is not as good as real data, but it’s a good starting point. Also, we are only using LLMs to generate the user inputs, not the LLM responses or internal system behavior.

Regardless of whether you use existing data or synthetic data, you want good coverage across the dimensions you’ve defined.

Incorporating System Information

When making test data, use your APIs and databases where appropriate. This will create realistic data and trigger the right scenarios. Sometimes you’ll need to write simple programs to get this information. That’s what the “Assumptions” column is referring to in the examples below.

Example LLM Prompts for Generating User Inputs
Here are some example prompts that illustrate how to use an LLM to generate synthetic user inputs for different combinations of features, scenarios, and personas:

ID	Feature	Scenario	Persona	LLM Prompt to Generate User Input	Assumptions (not directly in the prompt)
1	Order Tracking	Invalid Data Provided	Frustrated Customer	“Generate a user input from someone who is clearly irritated and impatient, using short, terse language to demand information about their order status for order number #**********. Include hints of previous negative experiences.”	Order number #********** does not exist in the system.
2	Contact Search	Multiple Matches Found	New User	“Create a user input from someone who seems unfamiliar with the system, using hesitant language and asking for help to find contact information for a person named ‘Alex’. The user should appear unsure about what information is needed.”	Multiple contacts named ‘Alex’ exist in the system.
3	Meeting Scheduler	Ambiguous Request	Busy Professional	“Simulate a user input from someone who is clearly in a hurry, using abbreviated language and minimal details to request scheduling a meeting. The message should feel rushed and lack specific information.”	N/A
4	Content Recommendation	No Matches Found	Expert User	“Produce a user input from someone who demonstrates in-depth knowledge of their industry, using specific terminology to request articles on sustainable supply chain management. Use the information in this article involving sustainable supply chain management to formulate a plausible query: {{article}}”	No articles on ‘Emerging trends in sustainable supply chain management’ exist in the system.
Generating Synthetic Data
When generating synthetic data, you only need to create the user inputs. You then feed these inputs into your AI system to generate the AI’s responses. It’s important that you log everything so you can evaluate your AI. To recap, here’s the process:

Generate User Inputs: Use the LLM prompts to create realistic user inputs.
Feed Inputs into Your AI System: Input the user interactions into your AI as it currently exists.
Capture AI Responses: Record the AI’s responses to form complete interactions.
Organize the Interactions: Create a table to store the user inputs, AI responses, and relevant metadata.
How much data should you generate?
There is no right answer here. At a minimum, you want to generate enough data so that you have examples for each combination of dimensions (in this toy example: features, scenarios, and personas). However, you also want to keep generating more data until you feel like you have stopped seeing new failure modes. The amount of data I generate varies significantly depending on the use case.

Does synthetic data actually work?
You might be skeptical of using synthetic data. After all, it’s not real data, so how can it be a good proxy? In my experience, it works surprisingly well. Some of my favorite AI products, like Hex use synthetic data to power their evals:

“LLMs are surprisingly good at generating excellent - and diverse - examples of user prompts. This can be relevant for powering application features, and sneakily, for building Evals. If this sounds a bit like the Large Language Snake is eating its tail, I was just as surprised as you! All I can say is: it works, ship it.” Bryan Bischof, Head of AI Engineering at Hex

Next Steps
With your dataset ready, now comes the most important part: getting your principal domain expert to evaluate the interactions.

Step 3: Direct The Domain Expert to Make Pass/Fail Judgments with Critiques
The domain expert’s job is to focus on one thing: “Did the AI achieve the desired outcome?” No complex scoring scales or multiple metrics. Just a clear pass or fail decision. In addition to the pass/fail decision, the domain expert should write a critique that explains their reasoning.

Why are simple pass/fail metrics important?
Clarity and Focus: A binary decision forces everyone to consider what truly matters. It simplifies the evaluation to a single, crucial question.

Actionable Insights: Pass/fail judgments are easy to interpret and act upon. They help you quickly identify whether the AI meets the user’s needs.

Forces Articulation of Expectations: When domain experts must decide if an interaction passes or fails, they are compelled to articulate their expectations clearly. This process uncovers nuances and unspoken assumptions about how the AI should behave.

Efficient Use of Resources: Keeps the evaluation process manageable, especially when starting out. You avoid getting bogged down in detailed metrics that might not be meaningful yet.

The Role of Critiques
Alongside a binary pass/fail judgment, it’s important to write a detailed critique of the LLM-generated output. These critiques:

Captures Nuances: The critique allows you to note if something was mostly correct but had areas for improvement.

Guide Improvement: Detailed feedback provides specific insights into how the AI can be enhanced.

Balance Simplicity with Depth: While the pass/fail offers a clear verdict, the critique offers the depth needed to understand the reasoning behind the judgment.

Why Write Critiques?:

In practice, domain experts may not have fully internalized all the judgment criteria. By forcing them to make a pass/fail decision and explain their reasoning, they clarify their expectations and provide valuable guidance for refining the AI.

“But my problem is complex!” Trust me—starting simple forces you to focus on what truly matters. You can introduce more complexity later if needed.

Examples of Good Critiques
To illustrate how simple pass/fail judgments combined with detailed critiques work in practice, here’s a table showcasing examples of user interactions with an AI assistant. The table includes both passes and fails, with critiques explaining why the AI received that judgment. In cases where the AI passed despite critical issues, the critique highlights these aspects and justifies why it still passed overall. For failed interactions, the critique explains the critical elements that led to the failure.

User Interaction with AI	Judgment	Critique
User: “I need to cancel my flight booking for next week.”
AI: “Your flight booking for next week has been canceled. A confirmation email has been sent to you.”	Pass	The AI successfully executed the user’s request by canceling the flight and sending a confirmation, achieving the primary goal. However, it should have asked for confirmation before proceeding to prevent accidental cancellations. Despite this critical oversight, the AI fulfilled the user’s request, so it passes, but adding a confirmation step would improve user safety.
User: “What’s the capital of France?”
AI: “The capital of France is Paris.”	Pass	The AI correctly answered the user’s question, providing accurate information promptly. Although the response is sufficient, the AI could enhance the interaction by offering additional interesting facts or asking if the user has more questions. Despite this opportunity for added engagement, the AI met the user’s primary need, so it passes.
User: “I can’t log into my account; it says my password is incorrect.”
AI: “Please reset your password by clicking ‘Forgot Password’.”	Fail	The AI provided a generic solution without acknowledging the user’s frustration or checking for other issues like account lockout. It failed to offer empathetic support or additional assistance. Due to the lack of personalized help and poor user experience, it fails.
User: “Book a table for two at an Italian restaurant tonight at 7 PM.”
AI: “Table for two at Luigi’s Italian Restaurant booked for tonight at 7 PM. Confirmation number is 12345.”	Pass	The AI efficiently completed the booking as requested, providing all necessary details and a confirmation number. However, it could have asked if the user had seating preferences or dietary restrictions. The AI accomplished the primary task successfully, so it passes, with minor opportunities for enhanced service.
User: “I need help with my billing statement.”
AI: “Please provide your account number and full name.”	Fail	The AI requested sensitive information without verifying the user’s identity through secure means, potentially compromising security. It should guide the user through a secure authentication process first. Due to this critical oversight in user data protection, it fails.
These examples demonstrate how the AI can receive both “Pass” and “Fail” judgments. In the critiques:

For passes, we explain why the AI succeeded in meeting the user’s primary need, even if there were critical aspects that could be improved. We highlight these areas for enhancement while justifying the overall passing judgment.

For fails, we identify the critical elements that led to the failure, explaining why the AI did not meet the user’s main objective or compromised important factors like user experience or security.

Most importantly, the critique should be detailed enough so that you can use it in a few-shot prompt for a LLM judge. In other words, it should be detailed enough that a new employee could understand it. Being too terse is a common mistake.

Note that the example user interactions with the AI are simplified for brevity - but you might need to give the domain expert more context to make a judgement. More on that later.

Note
At this point, you don’t need to perform a root cause analysis into the technical reasons behind why the AI failed. Many times, it’s useful to get a sense of overall behavior before diving into the weeds.

Don’t stray from binary pass/fail judgments when starting out
A common mistake is straying from binary pass/fail judgments. Let’s revisit the dashboard from earlier:



If your evaluations consist of a bunch of metrics that LLMs score on a 1-5 scale (or any other scale), you’re doing it wrong. Let’s unpack why.

It’s not actionable: People don’t know what to do with a 3 or 4. It’s not immediately obvious how this number is better than a 2. You need to be able to say “this interaction passed because…” and “this interaction failed because…”.
More often than not, these metrics do not matter. Every time I’ve analyzed data on domain expert judgments, they tend not to correlate with these kind of metrics. By having a domain expert make a binary judgment, you can figure out what truly matters.
This is why I hate off the shelf metrics that come with many evaluation frameworks. They tend to lead people astray.

Common Objections to Pass/Fail Judgments:

“The business said that these 8 dimensions are important, so we need to evaluate all of them.”
“We need to be able to say why an interaction passed or failed.”
I can guarantee you that if someone says you need to measure 8 things on a 1-5 scale, they don’t know what they are looking for. They are just guessing. You have to let the domain expert drive and make a pass/fail judgment with critiques so you can figure out what truly matters. Stand your ground here.

Make it easy for the domain expert to review data
Finally, you need to remove all friction from reviewing data. I’ve written about this here. Sometimes, you can just use a spreadsheet. It’s a judgement call in terms of what is easiest for the domain expert. I found that I often have to provide additional context to help the domain expert understand the user interaction, such as:

Metadata about the user, such as their location, subscription tier, etc.
Additional context about the system, such as the current time, inventory levels, etc.
Resources so you can check if the AI’s response is correct (ex: ability to search a database, etc.)
All of this data needs to be presented on a single screen so the domain expert can review it without jumping through hoops. That’s why I recommend building a simple web app to review data.

How many examples do you need?
The number of examples you need depends on the complexity of the task. My heuristic is that I start with around 30 examples and keep going until I do not see any new failure modes. From there, I keep going until I’m not learning anything new.

Next, we’ll look at how to use this data to build an LLM judge.

Step 4: Fix Errors
After looking at the data, it’s likely you will find errors in your AI system. Instead of plowing ahead and building an LLM judge, you want to fix any obvious errors. Remember, the whole point of the LLM as a judge is to help you find these errors, so it’s totally fine if you find them earlier!

If you have already developed Level 1 evals as outlined in my previous post, you should not have any pervasive errors. However, these errors can sometimes slip through the cracks. If you find pervasive errors, fix them and go back to step 3. Keep iterating until you feel like you have stabilized your system.

Step 5: Build Your LLM as A Judge, Iteratively
The Hidden Power of Critiques
You cannot write a good judge prompt until you’ve seen the data. The paper from Shankar et al., “Who Validates the Validators? Aligning LLM-Assisted Evaluation of LLM Outputs with Human Preferences” summarizes this well:

to grade outputs,people need to externalize and define their evaluation criteria; however, the process of grading outputs helps them to define that very criteria. We dub this phenomenon criteria drift, and it implies thatit is impossible to completely determine evaluation criteria prior to human judging of LLM outputs.

Start with Expert Examples
Let me share a real-world example of building an LLM judge you can apply to your own use case. When I was helping Honeycomb build their Query Assistant feature, we needed a way to evaluate if the AI was generating good queries. Here’s what our LLM judge prompt looked like, including few-shot examples of critiques from our domain expert, Phillip:

You are a Honeycomb query evaluator with advanced capabilities to judge if a query is good or not.
You understand the nuances of the Honeycomb query language, including what is likely to be
most useful from an analytics perspective. 

Here is information about the Honeycomb query language:
{{query_language_info}}

Here are some guidelines for evaluating queries:
{{guidelines}}

Example evaluations:

<examples>

<example-1>
<nlq>show me traces where ip is *********</nlq>
<query>
{
  "breakdowns": ["trace.trace_id"],
  "calculations": [{"op": "COUNT"}],
  "filters": [{"column": "net.host.ip", "op": "=", "value": "*********"}]
}
</query>
<critique>
{
  "critique": "The query correctly filters for traces with an IP address of ********* 
   and counts the occurrences of those traces, grouped by trace.trace_id. The response 
   is good as it meets the requirement of showing traces from a specific IP address 
   without additional complexities.",
  "outcome": "good"
}
</critique>
</example-1>

<example-2>
<nlq>show me slowest trace</nlq>
<query>
{
  "calculations": [{"column": "duration_ms", "op": "MAX"}],
  "orders": [{"column": "duration_ms", "op": "MAX", "order": "descending"}],
  "limit": 1
}
</query>
<critique>
{
  "critique": "While the query attempts to find the slowest trace using MAX(duration_ms) 
   and ordering correctly, it fails to group by trace.trace_id. Without this grouping, 
   the query only shows the MAX(duration_ms) measurement over time, not the actual 
   slowest trace.",
  "outcome": "bad"
}
</critique>
</example-2>

<example-3>
<nlq>count window-hash where window-hash exists per hour</nlq>
<query>
{
  "breakdowns": ["window-hash"],
  "calculations": [{"op": "COUNT"}],
  "filters": [{"column": "window-hash", "op": "exists"}],
  "time_range": 3600
}
</query>
<critique>
{
  "critique": "While the query correctly counts window-hash occurrences, the time_range 
   of 3600 seconds (1 hour) is insufficient for per-hour analysis. When we say 'per hour', 
   we need a time_range of at least 36000 seconds to show meaningful hourly patterns.",
  "outcome": "bad"
}
</critique>
</example-3>

</examples>

For the following query, first write a detailed critique explaining your reasoning,
then provide a pass/fail judgment in the same format as above.

<nlq>{{user_input}}</nlq>
<query>
{{generated_query}}
</query>
<critique>
Notice how each example includes:

The natural language query (NLQ) in <nlq> tags
The generated query in <query> tags
The critique and outcome in <critique> tags
In the prompt above, the example critiques are fixed. An advanced approach is to include examples dynamically based upon the item you are judging. You can learn more in this post about Continual In-Context Learning.

Keep Iterating on the Prompt Until Convergence With Domain Expert
In this case, I used a low-tech approach to iterate on the prompt. I sent Phillip a spreadsheet with the following information:

The NLQ
The generated query
The critique
The outcome (pass or fail)
Phillip would then fill out his own version of the spreadsheet with his critiques. I used this to iteratively improve the prompt. The spreadsheet looked like this:



I also tracked agreement rates over time to ensure we were converging on a good prompt.



It took us only three iterations to achieve > 90% agreement between the LLM and Phillip. Your mileage may vary depending on the complexity of the task. For example, Swyx has conducted a similar process hundreds of times for AI News, an extremely popular news aggregator with high quality recommendations. The quality of the AI owing to this process is why this product has received critical acclaim.

How to Optimize the LLM Judge Prompt?
I usually adjust the prompts by hand. I haven’t had much luck with prompt optimizers like DSPy. However, my friend Eugene Yan has just released a promising tool named ALIGN Eval. I like it because it’s simple and effective. Also, don’t forget the approach of continual in-context learning mentioned earlier - it can be effective when implemented correctly.

In rare cases, I might fine-tune a judge, but I prefer not to. I talk about this more in the FAQ section.

The Human Side of the Process
Something unexpected happened during this process. Phillip Carter, our domain expert at Honeycomb, found that reviewing the LLM’s critiques helped him articulate his own evaluation criteria more clearly. He said,

“Seeing how the LLM breaks down its reasoning made me realize I wasn’t being consistent about how I judged certain edge cases.”

This is a pattern I’ve seen repeatedly—the process of building an LLM judge often helps standardize evaluation criteria.

Furthermore, because this process forces the domain expert to look at data carefully, I always uncover new insights about the product, AI capabilities, and user needs. The resulting benefits are often more valuable than creating a LLM judge!

How Often Should You Evaluate?
I conduct this human review at regular intervals and whenever something material changes. For example, if I update a model, I’ll run the process again. I don’t get too scientific here; instead, I rely on my best judgment. Also note that after the first two iterations, I tend to focus more on errors rather than sampling randomly. For example, if I find an error, I’ll search for more examples that I think might trigger the same error. However, I always do a bit of random sampling as well.

What if this doesn’t work?
I’ve seen this process fail when:

The AI is overscoped: Example - a chatbot in a SaaS product that promises to do anything you want.
The process is not followed correctly: Not using the principal domain expert, not writing proper critiques, etc.
The expectations of alignment are unrealistic or not feasible.
In each of these cases, I try to address the root cause instead of trying to force alignment. Sometimes, you may not be able to achieve the alignment you want and may have to lean heavier on human annotations. However, after following the process described here, you will have metrics that help you understand how much you can trust the LLM judge.

Mistakes I’ve noticed in LLM judge prompts
Most of the mistakes I’ve seen in LLM judge prompts have to do with not providing good examples:

Not providing any critiques.
Writing extremely terse critiques.
Not providing external context. Your examples should contain the same information you use to evaluate, including external information like user metadata, system information etc.
Not providing diverse examples. You need a wide variety of examples to ensure that your judge works for a wide variety of inputs.
Sometimes, you may encounter difficulties with fitting everything you need into the prompt, and may have to get creative about how you structure the examples. However, this is becoming less of an issue thanks to expanding context windows and prompt caching.

Step 6: Perform Error Analysis
After you have created a LLM as a judge, you will have a dataset of user interactions with the AI, and the LLM’s judgments. If your metrics show an acceptable agreement between the domain expert and the LLM judge, you can apply this judge against real or synthetic interactions. After this, you can you calculate error rates for different dimensions of your data. You should calculate the error on unseen data only to make sure your aren’t getting biased results.

For example, if you have segmented your data by persona, scenario, feature, etc, your data analysis may look like this

Error Rates by Key Dimensions

Feature	Scenario	Persona	Total Examples	Failure Rate
Order Tracking	Multiple Matches	New User	42	24.3%
Order Tracking	Multiple Matches	Expert User	38	18.4%
Order Tracking	No Matches	Expert User	30	23.3%
Order Tracking	No Matches	New User	20	75.0%
Contact Search	Multiple Matches	New User	35	22.9%
Contact Search	Multiple Matches	Expert User	32	19.7%
Contact Search	No Matches	New User	25	68.0%
Contact Search	No Matches	Expert User	28	21.4%
Classify Traces
Once you know where the errors are now you can perform an error analysis to get to the root cause of the errors. My favorite way is to look at examples of each type of error and classify them by hand. I recommend using a spreadsheet for this. For example, a trace for Order tracking where there are no matches for new users might look like this:

Example Trace
In this example trace, the user provides an invalid order number. The AI correctly identifies that the order number is invalid but provides an unhelpful response. If you are not familiar with logging LLM traces, refer to my previous post on evals.

Note that this trace is formatted for readability.

{
 "user_input": "Where's my order #ABC123?",
 "function_calls": [
   {
     "name": "search_order_database",
     "args": {"order_id": "ABC123"},
     "result": {
       "status": "not_found",
       "valid_patterns": ["XXX-XXX-XXX"]
     }
   },
   {
     "name": "retrieve_context",
     "result": {
       "relevant_docs": [
         "Order numbers follow format XXX-XXX-XXX",
         "New users should check confirmation email"
       ]
     }
   }
 ],
 "llm_intermediate_steps": [
   {
     "thought": "User is new and order format is invalid",
     "action": "Generate help message with format info"
   }
 ],
 "final_response": "I cannot find that order #. Please check the number and try again."
}

In this case, you might classify the error as: Missing User Education. The system retrieved new user context and format information but failed to include it in the response, which suggests we could improve our prompt. After you have classified a number of errors, you can calculate the distribution of errors by root cause. That might look like this:

Root Cause Distribution (20 Failed Interactions)

Root Cause	Count	Percentage
Missing User Education	8	40%
Authentication/Access Issues	6	30%
Poor Context Handling	4	20%
Inadequate Error Messages	2	10%
Now you know where to focus your efforts. This doesn’t have to take an extraordinary amount of time. You can get quite far in just 15 minutes. Also, you can use a LLM to help you with this classification, but that is beyond the scope of this post (you can use a LLM to help you do anything in this post, as long as you have a process to verify the results).

An Interactive Walkthrough of Error Analysis
Error analysis has been around in Machine Learning for quite some time. This video by Andrew Ng does a great job of walking through the process interactively:


Fix Your Errors, Again
Now that you have a sense of the errors, you can go back and fix them again. Go back to step 3 and iterate until you are satisfied. Note that every time you fix an error, you should try to write a test case for it. Sometimes, this can be an assertion in your test suite, but other times you may need to create a more “specialized” LLM judge for these failures. We’ll talk about this next.

Doing this well requires data literacy
Investigating your data is much harder in practice than I made it look in this post. It requires a nose for data that only comes from practice. It also helps to have some basic familiarity with statistics and data analysis tools. My favorite post on data literacy is this one by Jason Liu and Eugene Yan.

Step 7: Create More Specialized LLM Judges (if needed)
Now that you have a sense for where the problems in your AI are, you can decide where and if to invest in more targeted LLM judges. For example, if you find that the AI has trouble with citing sources correctly, you can created a targeted eval for that. You might not even need a LLM judge for some errors (and use a code-based assertion instead).

The key takeaway is don’t jump directly to using specialized LLM judges until you have gone through this critique shadowing process. This will help you rationalize where to invest your time.

Recap of Critique Shadowing
Using an LLM as a judge can streamline your AI evaluation process if approached correctly. Here’s a visual illustration of the process (there is a description of the process below the diagram as well):

Yes

No

No

Yes

Yes

No

Yes

Start

1 Find Principal Domain Expert

2 Create Dataset

3 Domain Expert Reviews Data

Found Errors?

4 Fix Errors

5 Build LLM Judge

Test Against Domain Expert

Acceptable Agreement?

Refine Prompt

6 Perform Error Analysis

Critical Issues Found?

7 Fix Issues & Create Specialized Judges

Material Changes or Periodic Review?

The Critique Shadowing process is iterative, with feedback loops. Let’s list out the steps:

Find Principal Domain Expert
Create A Dataset
Generate diverse examples covering your use cases
Include real or synthetic user interactions
Domain Expert Reviews Data
Expert makes pass/fail judgments
Expert writes detailed critiques explaining their reasoning
Fix Errors (if found)
Address any issues discovered during review
Return to expert review to verify fixes
Go back to step 3 if errors are found
Build LLM Judge
Create prompt using expert examples
Test against expert judgments
Refine prompt until agreement is satisfactory
Perform Error Analysis
Calculate error rates across different dimensions
Identify patterns and root causes
Fix errors and go back to step 3 if needed
Create specialized judges as needed
This process never truly ends. It repeats periodically or when material changes occur.

It’s Not The Judge That Created Value, Afterall
The real value of this process is looking at your data and doing careful analysis. Even though an AI judge can be a helpful tool, going through this process is what drives results. I would go as far as saying that creating a LLM judge is a nice “hack” I use to trick people into carefully looking at their data!

That’s right. The real business value comes from looking at your data. But hey, potato, potahto.

Do You Really Need This?
Phew, this seems like a lot of work! Do you really need this? Well, it depends. There are cases where you can take a shortcut through this process. For example, let’s say:

You are an independent developer who is also a domain expert.
You are working with test data that already available. (Tweets, etc.)
Looking at data is not costly (etc. you can manually look at enough data in a few hours)
In this scenario, you can jump directly to something that looks like step 3 and start looking at data right away. Also, since it’s not that costly to look at data, it’s probably fine to just do error analysis without a judge (at least initially). You can incorporate what you learn directly back into your primary model right away. This example is not exhaustive, but gives you an idea of how you can adapt this process to your needs.

However, you can never completely eliminate looking at your data! This is precisely the step that most people skip. Don’t be that person.

FAQ
I received a lot of questions about this topic. Here are answers to the most common ones:

If I have a good judge LLM, isn’t that also the LLM I’d also want to use?
Effective judges often use larger models or more compute (via longer prompts, chain-of-thought, etc.) than the systems they evaluate.

However, If the cost of the most powerful LLM is not prohibitive, and latency is not an issue, then you might want to consider where you invest your efforts differently. In this case, it might make sense to put more effort towards specialist LLM judges, code-based assertions, and A/B testing. However, you should still go through the process of looking at data and critiquing the LLM’s output before you adopt specialized judges.

Do you recommend fine-tuning judges?
I prefer not to fine-tune LLM judges. I’d rather spend the effort fine-tuning the actual LLM instead. However, fine-tuning guardrails or other specialized judges can be useful (especially if they are small classifiers).

As a related note, you can leverage a LLM judge to curate and transform data for fine-tuning your primary model. For example, you can use the judge to:

Eliminate bad examples for fine-tuning.
Generate higher quality outputs (by referencing the critique).
Simulate high quality chain-of-thought with critiques.
Using a LLM judge for enhancing fine-tuning data is even more compelling when you are trying to distill a large LLM into a smaller one. The details of fine-tuning are beyond the scope of this post. If you are interested in learning more, see these resources.

What’s wrong with off-the-shelf LLM judges?
Nothing is strictly wrong with them. It’s just that many people are led astray by them. If you are disciplined you can apply them to your data and see if they are telling you something valuable. However, I’ve found that these tend to cause more confusion than value.

How Do you evaluate the LLM judge?
You will collect metrics on the agreement between the domain expert and the LLM judge. This tells you how much you can trust the judge and in what scenarios. Your domain expert doesn’t have to inspect every single example, you just need a representative sample so you can have reliable statistics.

What model do you use for the LLM judge?
For the kind of judge articulated in this blog post, I like to use the most powerful model I can afford in my cost/latency budget. This budget might be different than my primary model, depending on the number of examples I need to score. This can vary significantly according to the use case.

What about guardrails?
Guardrails are a separate but related topic. They are a way to prevent the LLM from saying/doing something harmful or inappropriate. This blog post focuses on helping you create a judge that’s aligned with business goals, especially when starting out.

I’m using LLM as a judge, and getting tremendous value but I didn’t follow this approach.
I believe you. This blog post is not the only way to use a LLM as a judge. In fact, I’ve seen people use a LLM as a judge in all sorts of creative ways, which include ranking, classification, model selection and so-on. I’m focused on an approach that works well when you are getting started, and avoids the pitfalls of confusing metric sprawl. However, the general process of looking at the data is still central no matter what kind of judge you are building.

How do you choose between traditional ML techniques, LLM-as-a-judge and human annotations?
The answer to this (and many other questions) is: do the simplest thing that works. And simple doesn’t always mean traditional ML techniques. Depending on your situation, it might be easier to use a LLM API as a classifier than to train a model and deploy it.

Can you make judges from small models?
Yes, potentially. I’ve only used the larger models for judges. You have to base the answer to this question on the data (i.e. the agreement with the domain expert).

How do you ensure consistency when updating your LLM model?
You have to go through the process again and measure the results.

How do you phase out human in the loop to scale this?
You don’t need a domain expert to grade every single example. You just need a representative sample. I don’t think you can eliminate humans completely, because the LLM still needs to be aligned to something, and that something is usually a human. As your evaluation system gets better, it naturally reduces the amount of human effort required.

---
Frequently Asked Questions (And Answers)
About AI Evals
Hamel Husain Shreya Shankar
2025-07-01
Contents
Q: Is RAG dead? . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
Q: Can I use the same model for both the main task and evaluation? . . . . . . . . . 3
Q: How much time should I spend on model selection? . . . . . . . . . . . . . . . . . 3
Q: Should I build a custom annotation tool or use something off-the-shelf? . . . . . . 3
Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings (Likert scales)? . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 4
Q: How do I debug multi-turn conversation traces? . . . . . . . . . . . . . . . . . . . 4
Q: Should I build automated evaluators for every failure mode I find? . . . . . . . . 5
Q: How many people should annotate my LLM outputs? . . . . . . . . . . . . . . . . 5
Q: What gaps in eval tooling should I be prepared to fill myself? . . . . . . . . . . . 6
Q: What is the best approach for generating synthetic data? . . . . . . . . . . . . . . 7
Q: How do I approach evaluation when my system handles diverse user queries? . . . 8
Q: How do I choose the right chunk size for my document processing tasks? . . . . . 8
Q: How should I approach evaluating my RAG system? . . . . . . . . . . . . . . . . 10
Q: What makes a good custom interface for reviewing LLM outputs? . . . . . . . . . 11
Q: How much of my development budget should I allocate to evals? . . . . . . . . . . 14
Q: Why is “error analysis” so important in LLM evals, and how is it performed? . . 15
Q: What’s the difference between guardrails & evaluators? . . . . . . . . . . . . . . . 15
Q: What’s a minimum viable evaluation setup? . . . . . . . . . . . . . . . . . . . . . 16
Q: How do I evaluate agentic workflows? . . . . . . . . . . . . . . . . . . . . . . . . . 16
Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor? . . . . . . 18
Q: How are evaluations used differently in CI/CD vs. monitoring production? . . . . 19
This post curates the most common questions Shreya and I have recieved from teaching 700+
engineers & PMs in AI Evals. Warning: These are sharp opinions about what works in most
cases. They are not universal truths. Use your judgment.
1
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
Q: Is RAG dead?
Question: Should I avoid using RAG for my AI application after reading that “RAG is dead”
for coding agents?
Many developers are confused about when and how to use RAG after reading
articles claiming “RAG is dead.” Understanding what RAG actually means versus
the narrow marketing definitions will help you make better architectural decisions
for your AI applications.
The viral article claiming RAG is dead specifically argues against using naive vector database
retrieval for autonomous coding agents, not RAG as a whole. This is a crucial distinction that
many developers miss due to misleading marketing.
RAG simply means Retrieval-Augmented Generation - using retrieval to provide relevant context that improves your model’s output. The core principle remains essential: your LLM needs
the right context to generate accurate answers. The question isn’t whether to use retrieval,
but how to retrieve effectively.
For coding applications, naive vector similarity search often fails because code relationships
are complex and contextual. Instead of abandoning retrieval entirely, modern coding assistants
like Claude Code still uses retrieval —they just employ agentic search instead of relying solely
on vector databases.similar to how human developers work.
You have multiple retrieval strategies available, ranging from simple keyword matching to
embedding similarity to LLM-powered relevance filtering. The optimal approach depends on
your specific use case, data characteristics, and performance requirements. Many production
systems combine multiple strategies or use multi-hop retrieval guided by LLM agents.
Unforunately, “RAG” has become a buzzword with no shared definition. Some people use it
to mean any retrieval system, others restrict it to vector databases. Focus on the fundamental
goal: getting your LLM the context it needs to succeed. Whether that’s through vector search,
agentic exploration, or hybrid approaches is a product and engineering decision that requires
understanding your users’ failure modes and usage patterns.
Rather than following categorical advice to avoid or embrace RAG, experiment with different
retrieval approaches and measure what works best for your application.
2
Q: Can I use the same model for both the main task and evaluation?
For LLM-as-Judge selection, using the same model is usually fine because the judge is doing
a different task than your main LLM pipeline. The judges we recommend building do scoped
binary classification tasks. Focus on achieving high True Positive Rate (TPR) and True
Negative Rate (TNR) with your judge on a held out labeled test set rather than avoiding the
same model family. You can use these metrics on the test set to understand how well your
judge is doing.
When selecting judge models, start with the most capable models available to establish strong
alignment with human judgments. You can optimize for cost later once you’ve established
reliable evaluation criteria. We do not recommend using the same model for open ended
preferences or response quality (but we don’t recommend building judges this way in the first
place!).
Q: How much time should I spend on model selection?
Many developers fixate on model selection as the primary way to improve their LLM applications. Start with error analysis to understand your failure modes before considering model
switching. As Hamel noted in office hours, “I suggest not thinking of switching model as the
main axes of how to improve your system off the bat without evidence. Does error analysis
suggest that your model is the problem?”
Q: Should I build a custom annotation tool or use something off-the-shelf?
Build a custom annotation tool. This is the single most impactful investment you can
make for your AI evaluation workflow. With AI-assisted development tools like Cursor or
Lovable, you can build a tailored interface in hours. I often find that teams with custom
annotation tools iterate ~10x faster.
Custom tools excel because:
• They show all your context from multiple systems in one place
• They can render your data in a product specific way (images, widgets, markdown, buttons, etc.)
• They’re designed for your specific workflow (custom filters, sorting, progress bars, etc.)
Off-the-shelf tools may be justified when you need to coordinate dozens of distributed annotators with enterprise access controls. Even then, many teams find the configuration overhead
and limitations aren’t worth it.
3
Isaac’s Anki flashcard annotation app shows the power of custom tools—handling 400+ results
per query with keyboard navigation and domain-specific evaluation criteria that would be
nearly impossible to configure in a generic tool.
Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings
(Likert scales)?
Engineers often believe that Likert scales (1-5 ratings) provide more information
than binary evaluations, allowing them to track gradual improvements. However,
this added complexity often creates more problems than it solves in practice.
Binary evaluations force clearer thinking and more consistent labeling. Likert scales introduce
significant challenges: the difference between adjacent points (like 3 vs 4) is subjective and
inconsistent across annotators, detecting statistical differences requires larger sample sizes, and
annotators often default to middle values to avoid making hard decisions.
Having binary options forces people to make a decision rather than hiding uncertainty in
middle values. Binary decisions are also faster to make during error analysis - you don’t waste
time debating whether something is a 3 or 4.
For tracking gradual improvements, consider measuring specific sub-components with their
own binary checks rather than using a scale. For example, instead of rating factual accuracy
1-5, you could track “4 out of 5 expected facts included” as separate binary checks. This
preserves the ability to measure progress while maintaining clear, objective criteria.
Start with binary labels to understand what ‘bad’ looks like. Numeric labels are advanced and
usually not necessary.
Q: How do I debug multi-turn conversation traces?
Start simple. Check if the whole conversation met the user’s goal with a pass/fail judgment.
Look at the entire trace and focus on the first upstream failure. Read the user-visible parts
first to understand if something went wrong. Only then dig into the technical details like tool
calls and intermediate steps.
When you find a failure, reproduce it with the simplest possible test case. Here’s a example:
suppose a shopping bot gives the wrong return policy on turn 4 of a conversation. Before
diving into the full multi-turn complexity, simplify it to a single turn: “What is the return
window for product X1000?” If it still fails, you’ve proven the error isn’t about conversation
context - it’s likely a basic retrieval or knowledge issue you can debug more easily.
For generating test cases, you have two main approaches. First, you can simulate users with
another LLM to create realistic multi-turn conversations. Second, use “N-1 testing” where
you provide the first N-1 turns of a real conversation and test what happens next. The N-1
4
approach often works better since it uses actual conversation prefixes rather than fully synthetic
interactions (but is less flexible and doesn’t test the full conversation). User simulation is
getting better as models improve. Keep an eye on this space.
The key is balancing thoroughness with efficiency. Not every multi-turn failure requires multiturn analysis.
Q: Should I build automated evaluators for every failure mode I find?
Focus automated evaluators on failures that persist after fixing your prompts. Many teams
discover their LLM doesn’t meet preferences they never actually specified - like wanting short
responses, specific formatting, or step-by-step reasoning. Fix these obvious gaps first before
building complex evaluation infrastructure.
Consider the cost hierarchy of different evaluator types. Simple assertions and referencebased checks (comparing against known correct answers) are cheap to build and maintain.
LLM-as-Judge evaluators require 100+ labeled examples, ongoing weekly maintenance, and
coordination between developers, PMs, and domain experts. This cost difference should shape
your evaluation strategy.
Only build expensive evaluators for problems you’ll iterate on repeatedly. Since LLM-as-Judge
comes with significant overhead, save it for persistent generalization failures - not issues you
can fix trivially. Start with cheap code-based checks where possible: regex patterns, structural
validation, or execution tests. Reserve complex evaluation for subjective qualities that can’t
be captured by simple rules.
Q: How many people should annotate my LLM outputs?
For most small to medium-sized companies, appointing a single domain expert as a “benevolent
dictator” is the most effective approach. This person—whether it’s a psychologist for a mental
health chatbot, a lawyer for legal document analysis, or a customer service director for support
automation—becomes the definitive voice on quality standards.
A single expert eliminates annotation conflicts and prevents the paralysis that comes from
“too many cooks in the kitchen”. The benevolent dictator can incorporate input and feedback
from others, but they drive the process. If you feel like you need five subject matter experts
to judge a single interaction, it’s a sign your product scope might be too broad.
However, larger organizations or those operating across multiple domains (like a multinational
company with different cultural contexts) may need multiple annotators. When you do use
multiple people, you’ll need to measure their agreement using metrics like Cohen’s Kappa,
which accounts for agreement beyond chance. However, use your judgment. Even in larger
companies, a single expert is often enough.
5
Start with a benevolent dictator whenever feasible. Only add complexity when your domain
demands it.
Q: What gaps in eval tooling should I be prepared to fill myself?
Most eval tools handle the basics well: logging complete traces, tracking metrics, prompt
playgrounds, and annotation queues. These are table stakes. Here are four areas where you’ll
likely need to supplement existing tools.
Watch for vendors addressing these gaps—it’s a strong signal they understand practitioner
needs.
1. Error Analysis and Pattern Discovery
After reviewing traces where your AI fails, can your tooling automatically cluster similar issues?
For instance, if multiple traces show the assistant using casual language for luxury clients, you
need something that recognizes this broader “persona-tone mismatch” pattern. We recommend
building capabilities that use AI to suggest groupings, rewrite your observations into clearer
failure taxonomies, help find similar cases through semantic search, etc.
2. AI-Powered Assistance Throughout the Workflow
The most effective workflows use AI to accelerate every stage of evaluation. During error
analysis, you want an LLM helping categorize your open-ended observations into coherent
failure modes. For example, you might annotate several traces with notes like “wrong tone for
investor,” “too casual for luxury buyer,” etc. Your tooling should recognize these as the same
underlying pattern and suggest a unified “persona-tone mismatch” category.
You’ll also want AI assistance in proposing fixes. After identifying 20 cases where your assistant omits pet policies from property summaries, can your workflow analyze these failures
and suggest specific prompt modifications? Can it draft refinements to your SQL generation
instructions when it notices patterns of missing WHERE clauses?
Additionally, good workflows help you conduct data analysis of your annotations and traces.
I like using notebooks with AI in-the-loop like Julius,Hex or SolveIt. These help me discover
insights like “location ambiguity errors spike 3x when users mention neighborhood names” or
“tone mismatches occur 80% more often in email generation than other modalities.”
6
3. Custom Evaluators Over Generic Metrics
Be prepared to build most of your evaluators from scratch. Generic metrics like “hallucination
score” or “helpfulness rating” rarely capture what actually matters for your application—
like proposing unavailable showing times or omitting budget constraints from emails. In our
experience, successful teams spend most of their effort on application-specific metrics.
4. APIs That Support Custom Annotation Apps
Custom annotation interfaces work best for most teams. This requires observability platforms
with thoughtful APIs. I often have to build my own libraries and abstractions just to make
bulk data export manageable. You shouldn’t have to paginate through thousands of requests
or handle timeout-prone endpoints just to get your data. Look for platforms that provide true
bulk export capabilities and, crucially, APIs that let you write annotations back efficiently.
Q: What is the best approach for generating synthetic data?
A common mistake is prompting an LLM to "give me test queries" without structure,
resulting in generic, repetitive outputs. A structured approach using dimensions produces far
better synthetic data for testing LLM applications.
Start by defining dimensions: categories that describe different aspects of user queries.
Each dimension captures one type of variation in user behavior. For example:
• For a recipe app, dimensions might include Dietary Restriction (vegan, gluten-free, none),
Cuisine Type (Italian, Asian, comfort food), and Query Complexity (simple request,
multi-step, edge case).
• For a customer support bot, dimensions could be Issue Type (billing, technical, general),
Customer Mood (frustrated, neutral, happy), and Prior Context (new issue, follow-up,
resolved).
Choose dimensions that target likely failure modes. If you suspect your recipe app
struggles with scaling ingredients for large groups or your support bot mishandles angry customers, make those dimensions. Use your application first—you need hypotheses about where
failures occur. Without this, you’ll generate useless test data.
Once you have dimensions, create tuples: specific combinations selecting one value from
each dimension. A tuple like (Vegan, Italian, Multi-step) represents a particular use case.
Write 20 tuples manually to understand your problem space, then use an LLM to scale up.
The two-step generation process is important. First, have the LLM generate structured tuples.
Then, in a separate prompt, convert each tuple to a natural language query. This separation
prevents repetitive phrasing. For the vegan Italian tuple above, you might get "I need a
dairy-free lasagna recipe that I can prep the day before."
7
Don’t generate synthetic data for problems you can fix immediately. If your prompt
never mentions handling dietary restrictions, fix the prompt rather than generating hundreds
of specialized queries. Save synthetic data for complex issues requiring iteration—like an LLM
consistently failing at ingredient scaling math or misinterpreting ambiguous requests.
After iterating on your tuples and prompts, run these synthetic queries through your
actual system to capture full traces. Sample 100 traces for error analysis. This number
provides enough traces to manually review and identify failure patterns without being overwhelming. Rather than generating thousands of similar queries, ensure your 100 traces cover
diverse combinations across your dimensions—this variety will reveal more failure modes than
sheer volume.
Q: How do I approach evaluation when my system handles diverse user queries?
Complex applications often support vastly different query patterns—from “What’s
the return policy?” to “Compare pricing trends across regions for products matching these criteria.” Each query type exercises different system capabilities, leading
to confusion on how to design eval criteria.
Error Analysis is all you need. Your evaluation strategy should emerge from observed
failure patterns (e.g. error analysis), not predetermined query classifications. Rather than
creating a massive evaluation matrix covering every query type you can imagine, let your
system’s actual behavior guide where you invest evaluation effort.
During error analysis, you’ll likely discover that certain query categories share failure patterns.
For instance, all queries requiring temporal reasoning might struggle regardless of whether
they’re simple lookups or complex aggregations. Similarly, queries that need to combine
information from multiple sources might fail in consistent ways. These patterns discovered
through error analysis should drive your evaluation priorities. It could be that query category
is a fine way to group failures, but you don’t know that until you’ve analyzed your data.
To see an example of basic error analysis in action, see this video.
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
Q: How do I choose the right chunk size for my document processing tasks?
Unlike RAG, where chunks are optimized for retrieval, document processing assumes the model
will see every chunk. The goal is to split text so the model can reason effectively without being
overwhelmed. Even if a document fits within the context window, it might be better to break
it up. Long inputs can degrade performance due to attention bottlenecks, especially in the
middle of the context. Two task types require different strategies:
8
1. Fixed-Output Tasks → Large Chunks
These are tasks where the output length doesn’t grow with input: extracting a number, answering a specific question, classifying a section. For example:
• “What’s the penalty clause in this contract?”
• “What was the CEO’s salary in 2023?”
Use the largest chunk (with caveats) that likely contains the answer. This reduces the number
of queries and avoids context fragmentation. However, avoid adding irrelevant text. Models are
sensitive to distraction, especially with large inputs. The middle parts of a long input might
be under-attended. Furthermore, if cost and latency are a bottleneck, you should consider
preprocessing or filtering the document (via keyword search or a lightweight retriever) to
isolate relevant sections before feeding a huge chunk.
2. Expansive-Output Tasks → Smaller Chunks
These include summarization, exhaustive extraction, or any task where output grows with
input. For example:
• “Summarize each section”
• “List all customer complaints”
In these cases, smaller chunks help preserve reasoning quality and output completeness. The
standard approach is to process each chunk independently, then aggregate results (e.g., mapreduce). When sizing your chunks, try to respect content boundaries like paragraphs, sections,
or chapters. Chunking also helps mitigate output limits. By breaking the task into pieces,
each piece’s output can stay within limits.
General Guidance
It’s important to recognize why chunk size affects results. A larger chunk means the model
has to reason over more information in one go – essentially, a heavier cognitive load. LLMs
have limited capacity to retain and correlate details across a long text. If too much
is packed in, the model might prioritize certain parts (commonly the beginning or end) and
overlook or “forget” details in the middle. This can lead to overly coarse summaries or missed
facts. In contrast, a smaller chunk bounds the problem: the model can pay full attention to
that section. You are trading off global context for local focus.
No rule of thumb can perfectly determine the best chunk size for your use case – you should
validate with experiments. The optimal chunk size can vary by domain and model. I treat
chunk size as a hyper parameter to tune.
9
Q: How should I approach evaluating my RAG system?
RAG systems have two distinct components that require different evaluation approaches: retrieval and generation.
The retrieval component is a search problem. Evaluate it using traditional information retrieval
(IR) metrics. Common examples include Recall@k (of all relevant documents, how many
did you retrieve in the top k?), Precision@k (of the k documents retrieved, how many were
relevant?), or MRR (how high up was the first relevant document?). The specific metrics you
choose depend on your use case. These metrics are pure search metrics that measure whether
you’re finding the right documents (more on this below).
To evaluate retrieval, create a dataset of queries paired with their relevant documents. Generate this synthetically by taking documents from your corpus, extracting key facts, then
generating questions those facts would answer. This reverse process gives you query-document
pairs for measuring retrieval performance without manual annotation.
For the generation component—how well the LLM uses retrieved context, whether it hallucinates, whether it answers the question—use the same evaluation procedures covered throughout this course: error analysis to identify failure modes, collecting human labels, building
LLM-as-judge evaluators, and validating those judges against human annotations.
Jason Liu’s “There Are Only 6 RAG Evals” provides a framework that maps well to this
separation. His Tier 1 covers traditional IR metrics for retrieval. Tiers 2 and 3 evaluate
relationships between Question, Context, and Answer—like whether the context is relevant
(C|Q), whether the answer is faithful to context (A|C), and whether the answer addresses the
question (A|Q).
In addition to Jason’s six evals, error analysis on your specific data may reveal domain-specific
failure modes that warrant their own metrics. For example, a medical RAG system might
consistently fail to distinguish between drug dosages for adults versus children, or a legal
RAG might confuse jurisdictional boundaries. These patterns emerge only through systematic
review of actual failures. Once identified, you can create targeted evaluators for these specific
issues beyond the general framework.
Finally, when implementing Jason’s Tier 2 and 3 metrics, don’t just use prompts off the shelf.
The standard LLM-as-judge process requires several steps: error analysis, prompt iteration,
creating labeled examples, and measuring your judge’s accuracy against human labels. Once
you know your judge’s True Positive and True Negative rates, you can correct its estimates
to determine the actual failure rate in your system. Skip this validation and your judges may
not reflect your actual quality criteria.
In summary, debug retrieval first using IR metrics, then tackle generation quality using properly validated LLM judges.
10
Q: What makes a good custom interface for reviewing LLM outputs?
Great interfaces make human review fast, clear, and motivating. We recommend building
your own annotation tool customized to your domain. The following features are possible
enhancements we’ve seen work well, but you don’t need all of them. The screenshots shown
are illustrative examples to clarify concepts. In practice, I rarely implement all these features
in a single app. It’s ultimately a judgment call based on your specific needs and constraints.
1. Render Traces Intelligently, Not Generically: Present the trace in a way that’s
intuitive for the domain. If you’re evaluating generated emails, render them to look like
emails. If the output is code, use syntax highlighting. Allow the reviewer to see the full
trace (user input, tool calls, and LLM reasoning), but keep less important details in collapsed
sections that can be expanded. Here is an example of a custom annotation tool for reviewing
real estate assistant emails:
Figure 1: A custom interface for reviewing emails for a real estate assistant.
2. Show Progress and Support Keyboard Navigation: Keep reviewers in a state of flow
by minimizing friction and motivating completion. Include progress indicators (e.g., “Trace
45 of 100”) to keep the review session bounded and encourage completion. Enable hotkeys for
11
navigating between traces (e.g., N for next), applying labels, and saving notes quickly. Below
is an illustration of these features:
Figure 2: An annotation interface with a progress bar and hotkey guide
4. Trace navigation through clustering, filtering, and search: Allow reviewers to
filter traces by metadata or search by keywords. Semantic search helps find conceptually
similar problems. Clustering similar traces (like grouping by user persona) lets reviewers spot
recurring issues and explore hypotheses. Below is an illustration of these features:
12
Figure 3: Cluster view showing groups of emails, such as property-focused or client-focused
examples. Reviewers can drill into a group to see individual traces.
5. Prioritize labeling traces you think might be problematic: Surface traces flagged by
guardrails, CI failures, or automated evaluators for review. Provide buttons to take actions like
adding to datasets, filing bugs, or re-running pipeline tests. Display relevant context (pipeline
version, eval scores, reviewer info) directly in the interface to minimize context switching.
Below is an illustration of these ideas:
13
Figure 4: A trace view that allows you to quickly see auto-evaluator verdict, add traces to
dataset or open issues. Also shows metadata like pipeline version, reviewer info, and
more.
General Principle: Keep it minimal
Keep your annotation interface minimal. Only incorporate these ideas if they provide a benefit
that outweighs the additional complexity and maintenance overhead.
Q: How much of my development budget should I allocate to evals?
It’s important to recognize that evaluation is part of the development process rather than a
distinct line item, similar to how debugging is part of software development.
You should always be doing error analysis. When you discover issues through error analysis,
many will be straightforward bugs you’ll fix immediately. These fixes don’t require separate
evaluation infrastructure as they’re just part of development.
The decision to build automated evaluators comes down to cost-benefit analysis. If you can
catch an error with a simple assertion or regex check, the cost is minimal and probably worth
it. But if you need to align an LLM-as-judge evaluator, consider whether the failure mode
warrants that investment.
In the projects we’ve worked on, we’ve spent 60-80% of our development time on error
analysis and evaluation. Expect most of your effort to go toward understanding failures
(i.e. looking at data) rather than building automated checks.
14
Be wary of optimizing for high eval pass rates. If you’re passing 100% of your evals, you’re
likely not challenging your system enough. A 70% pass rate might indicate a more meaningful
evaluation that’s actually stress-testing your application. Focus on evals that help you catch
real issues, not ones that make your metrics look good.
Q: Why is “error analysis” so important in LLM evals, and how is it performed?
Error analysis is the most important activity in evals. Error analysis helps you decide
what evals to write in the first place. It allows you to identify failure modes unique to your
application and data. The process involves:
1. Creating a Dataset: Gathering representative traces of user interactions with the LLM.
If you do not have any data, you can generate synthetic data to get started.
2. Open Coding: Human annotator(s) (ideally a benevolent dictator) review and write
open-ended notes about traces, noting any issues. This process is akin to “journaling” and
is adapted from qualitative research methodologies. When beginning, it is recommended
to focus on noting the first failure observed in a trace, as upstream errors can cause
downstream issues, though you can also tag all independent failures if feasible. A domain
expert should be performing this step.
3. Axial Coding: Categorize the open-ended notes into a “failure taxonomy.”. In other
words, group similar failures into distinct categories. This is the most important step.
At the end, count the number of failures in each category. You can use a LLM to help
with this step.
4. Iterative Refinement: Keep iterating on more traces until you reach theoretical saturation, meaning new traces do not seem to reveal new failure modes or information to
you. As a rule of thumb, you should aim to review at least 100 traces.
You should frequently revisit this process. There are advanced ways to sample data more
efficiently, like clustering, sorting by user feedback, and sorting by high probability failure
patterns. Over time, you’ll develop a “nose” for where to look for failures in your data.
Do not skip error analysis. It ensures that the evaluation metrics you develop are supported by
real application behaviors instead of counter-productive generic metrics (which most platforms
nudge you to use). For examples of how error analysis can be helpful, see this video, or this
blog post.
Q: What’s the difference between guardrails & evaluators?
Guardrails are inline safety checks that sit directly in the request/response path. They
validate inputs or outputs before anything reaches a user, so they typically are:
15
• Fast and deterministic – typically a few milliseconds of latency budget.
• Simple and explainable – regexes, keyword block-lists, schema or type validators,
lightweight classifiers.
• Targeted at clear-cut, high-impact failures – PII leaks, profanity, disallowed instructions, SQL injection, malformed JSON, invalid code syntax, etc.
If a guardrail triggers, the system can redact, refuse, or regenerate the response. Because these
checks are user-visible when they fire, false positives are treated as production bugs; teams
version guardrail rules, log every trigger, and monitor rates to keep them conservative.
On the other hand, evaluators typically run after a response is produced. Evaluators measure
qualities that simple rules cannot, such as factual correctness, completeness, etc. Their verdicts
feed dashboards, regression tests, and model-improvement loops, but they do not block the
original answer.
Evaluators are usually run asynchronously or in batch to afford heavier computation such as
a LLM-as-a-Judge. Inline use of an LLM-as-Judge is possible only when the latency budget
and reliability targets allow it. Slow LLM judges might be feasible in a cascade that runs on
the minority of borderline cases.
Apply guardrails for immediate protection against objective failures requiring intervention.
Use evaluators for monitoring and improving subjective or nuanced criteria. Together, they
create layered protection.
Word of caution: Do not use llm guardrails off the shelf blindly. Always look at the prompt.
Q: What’s a minimum viable evaluation setup?
Start with error analysis, not infrastructure. Spend 30 minutes manually reviewing 20-50 LLM
outputs whenever you make significant changes. Use one domain expert who understands your
users as your quality decision maker (a “benevolent dictator”).
If possible, use notebooks to help you review traces and analyze data. In our opinion, this
is the single most effective tool for evals because you can write arbitrary code, visualize data,
and iterate quickly. You can even build your own custom annotation interface right inside
notebooks, as shown in this video.
Q: How do I evaluate agentic workflows?
We recommend evaluating agentic workflows in two phases:
1. End-to-end task success. Treat the agent as a black box and ask “did we meet the
user’s goal?”. Define a precise success rule per task (exact answer, correct side-effect, etc.)
16
and measure with human or aligned LLM judges. Take note of the first upstream failure when
conducting error analysis.
Once error analysis reveals which workflows fail most often, move to step-level diagnostics to
understand why they’re failing.
2. Step-level diagnostics. Assuming that you have sufficiently instrumented your system
with details of tool calls and responses, you can score individual components such as: - Tool
choice: was the selected tool appropriate? - Parameter extraction: were inputs complete and
well-formed? - Error handling: did the agent recover from empty results or API failures? -
Context retention: did it preserve earlier constraints? - Efficiency: how many steps, seconds,
and tokens were spent? - Goal checkpoints: for long workflows verify key milestones.
Example: “Find Berkeley homes under $1M and schedule viewings” breaks into: parameters
extracted correctly, relevant listings retrieved, availability checked, and calendar invites sent.
Each checkpoint can pass or fail independently, making debugging tractable.
Use transition failure matrices to understand error patterns. Create a matrix where
rows represent the last successful state and columns represent where the first failure occurred.
This is a great way to understand where the most failures occur.
Figure 5: Transition failure matrix showing hotspots in text-to-SQL agent workflow
Transition matrices transform overwhelming agent complexity into actionable insights. Instead
of drowning in individual trace reviews, you can immediately see that GenSQL → ExecSQL
transitions cause 12 failures while DecideTool → PlanCal causes only 2. This data-driven
17
approach guides where to invest debugging effort. Here is another example from Bryan Bischof,
that is also a text-to-SQL agent:
Figure 6: Bischof, Bryan “Failure is A Funnel - Data Council, 2025”
In this example, Bryan shows variation in transition matrices across experiments. How you
organize your transition matrix depends on the specifics of your application. For example,
Bryan’s text-to-SQL agent has an inherent sequential workflow which he exploits for further
analytical insight. You can watch his full talk for more details.
Creating Test Cases for Agent Failures
Creating test cases for agent failures follows the same principles as our previous FAQ on
debugging multi-turn conversation traces (i.e. try to reproduce the error in the simplest way
possible, only use multi-turn tests when the failure actually requires conversation context,
etc.).
Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor?
Eval tools are in an intensely competitive space. It would be futile to compare their features.
If I tried to do such an analysis, it would be invalidated in a week! Vendors I encounter the
most organically in my work are: Langsmith, Arize and Braintrust.
When I help clients with vendor selection, the decision weighs heavily towards who can offer
the best support, as opposed to purely features. This changes depending on size of client, use
case, etc. Yes - its mainly the human factor that matters, and dare I say, vibes.
I have no favorite vendor. At the core, their features are very similar - and I often build custom
tools on top of them to fit my needs.
My suggestion is to explore the vendors and see which one you like the most.
18
Q: How are evaluations used differently in CI/CD vs. monitoring production?
The most important difference between CI vs. production evaluation is the data used for
testing.
Test datasets for CI are small (in many cases 100+ examples) and purpose-built. Examples
cover core features, regression tests for past bugs, and known edge cases. Since CI tests are run
frequently, the cost of each test has to be carefully considered (that’s why you carefully curate
the dataset). Favor assertions or other deterministic checks over LLM-as-judge evaluators.
For evaluating production traffic, you can sample live traces and run evaluators against them
asynchronously. Since you usually lack reference outputs on production data, you might rely
more on on more expensive reference-free evaluators like LLM-as-judge. Additionally, track
confidence intervals for production metrics. If the lower bound crosses your threshold, investigate further.
These two systems are complementary: when production monitoring reveals new failure patterns through error analysis and evals, add representative examples to your CI dataset. This
mitigates regressions on new issues.
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
19

--
