"""
Enhanced PR Creation with Robust Error Handling
Addresses common PR creation failures in autonomous coding agent
"""

import logging
import re
import json
from typing import Dict, Any, Optional
import asyncio

logger = logging.getLogger(__name__)


class PRCreationFix:
    """Enhanced PR creation with comprehensive error handling"""
    
    def __init__(self, sandbox_manager):
        self.sandbox = sandbox_manager
    
    async def create_pr_with_fallbacks(self, session_id: str, title: str, 
                                     body: str, branch_name: str, 
                                     repo_path: str, github_token: str) -> Dict[str, Any]:
        """
        Create PR with multiple fallback strategies
        """
        
        # Strategy 1: GitHub CLI with proper authentication
        result = await self._try_github_cli(session_id, title, body, branch_name, repo_path, github_token)
        if result["success"]:
            return result
        
        # Strategy 2: GitHub API via curl
        result = await self._try_github_api(session_id, title, body, branch_name, repo_path, github_token)
        if result["success"]:
            return result
        
        # Strategy 3: Manual PR URL generation
        result = await self._generate_manual_pr_url(session_id, branch_name, repo_path)
        if result["success"]:
            return result
        
        # All strategies failed
        return {
            "success": False,
            "error": "All PR creation strategies failed",
            "details": result.get("error", "Unknown error")
        }
    
    async def _try_github_cli(self, session_id: str, title: str, body: str, 
                            branch_name: str, repo_path: str, github_token: str) -> Dict[str, Any]:
        """Try GitHub CLI approach"""
        try:
            # Ensure gh is authenticated
            auth_cmd = f"echo '{github_token}' | gh auth login --with-token"
            auth_result = await self.sandbox.execute_command(session_id, auth_cmd, working_dir=repo_path)
            
            if not auth_result["success"]:
                return {"success": False, "error": f"GH auth failed: {auth_result['stderr']}"}
            
            # Create PR with proper escaping
            escaped_title = title.replace('"', '\\"').replace("'", "\\'")
            escaped_body = body.replace('"', '\\"').replace("'", "\\'")
            
            # Use heredoc for body to handle newlines
            pr_cmd = f"""gh pr create --title "{escaped_title}" --body-file - --head {branch_name} --base main << 'EOF'
{body}
EOF"""
            
            pr_result = await self.sandbox.execute_command(session_id, pr_cmd, working_dir=repo_path)
            
            if pr_result["success"]:
                pr_url = pr_result["stdout"].strip()
                return {"success": True, "pr_url": pr_url, "method": "github_cli"}
            else:
                return {"success": False, "error": f"GH CLI failed: {pr_result['stderr']}"}
                
        except Exception as e:
            return {"success": False, "error": f"GitHub CLI exception: {str(e)}"}
    
    async def _try_github_api(self, session_id: str, title: str, body: str, 
                            branch_name: str, repo_path: str, github_token: str) -> Dict[str, Any]:
        """Try GitHub REST API via curl"""
        try:
            # Get repository info
            remote_result = await self.sandbox.execute_command(
                session_id, 
                "git remote get-url origin", 
                working_dir=repo_path
            )
            
            if not remote_result["success"]:
                return {"success": False, "error": "Could not get remote URL"}
            
            remote_url = remote_result["stdout"].strip()
            
            # Parse owner and repo
            match = re.search(r'github\.com[:/]([^/]+)/([^/.]+)', remote_url)
            if not match:
                return {"success": False, "error": "Could not parse repository URL"}
            
            owner = match.group(1)
            repo = match.group(2)
            
            # Create PR via GitHub API
            api_payload = {
                "title": title,
                "body": body,
                "head": branch_name,
                "base": "main"
            }
            
            escaped_payload = json.dumps(api_payload).replace('"', '\\"')
            
            api_cmd = f"""
curl -X POST \
  -H "Authorization: token {github_token}" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/{owner}/{repo}/pulls \
  -d "{escaped_payload}"
"""
            
            api_result = await self.sandbox.execute_command(session_id, api_cmd, working_dir=repo_path)
            
            if api_result["success"]:
                response = api_result["stdout"].strip()
                try:
                    response_json = json.loads(response)
                    if "html_url" in response_json:
                        return {"success": True, "pr_url": response_json["html_url"], "method": "github_api"}
                    else:
                        return {"success": False, "error": f"API response missing URL: {response}"}
                except json.JSONDecodeError:
                    return {"success": False, "error": f"Invalid API response: {response}"}
            else:
                return {"success": False, "error": f"GitHub API failed: {api_result['stderr']}"}
                
        except Exception as e:
            return {"success": False, "error": f"GitHub API exception: {str(e)}"}
    
    async def _generate_manual_pr_url(self, session_id: str, branch_name: str, 
                                    repo_path: str) -> Dict[str, Any]:
        """Generate manual PR URL as fallback"""
        try:
            # Get repository info
            remote_result = await self.sandbox.execute_command(
                session_id, 
                "git remote get-url origin", 
                working_dir=repo_path
            )
            
            if not remote_result["success"]:
                return {"success": False, "error": "Could not get remote URL"}
            
            remote_url = remote_result["stdout"].strip()
            
            # Convert to HTTPS format
            if remote_url.startswith("git@"):
                https_url = remote_url.replace("git@", "https://").replace(":", "/")
            else:
                https_url = remote_url
            
            if https_url.endswith(".git"):
                https_url = https_url[:-4]
            
            # Generate manual PR URL
            manual_url = f"{https_url}/compare/{branch_name}?expand=1"
            
            return {
                "success": True, 
                "pr_url": manual_url, 
                "method": "manual_url",
                "note": "Manual PR creation required"
            }
            
        except Exception as e:
            return {"success": False, "error": f"Manual URL generation failed: {str(e)}"}
    
    async def validate_prerequisites(self, session_id: str, repo_path: str) -> Dict[str, Any]:
        """Validate all PR creation prerequisites"""
        checks = {}
        
        # Check if git is available
        git_check = await self.sandbox.execute_command(session_id, "git --version", working_dir=repo_path)
        checks["git_available"] = git_check["success"]
        
        # Check if repository is clean
        status_check = await self.sandbox.execute_command(session_id, "git status --porcelain", working_dir=repo_path)
        checks["has_changes"] = bool(status_check["stdout"].strip())
        
        # Check if branch exists and is pushed
        branch_check = await self.sandbox.execute_command(session_id, f"git branch --list", working_dir=repo_path)
        checks["branch_exists"] = branch_name in branch_check["stdout"]
        
        # Check if branch is pushed
        remote_check = await self.sandbox.execute_command(session_id, f"git ls-remote --heads origin {branch_name}", working_dir=repo_path)
        checks["branch_pushed"] = bool(remote_check["stdout"].strip())
        
        # Check GitHub CLI
        gh_check = await self.sandbox.execute_command(session_id, "gh --version", working_dir=repo_path)
        checks["gh_available"] = gh_check["success"]
        
        return {
            "success": all(checks.values()),
            "checks": checks,
            "details": "All prerequisites validated" if all(checks.values()) else "Some prerequisites failed"
        }
    
    async def fix_common_issues(self, session_id: str, repo_path: str, 
                              branch_name: str, github_token: str) -> Dict[str, Any]:
        """Fix common PR creation issues"""
        fixes = []
        
        # Fix 1: Ensure branch is pushed
        push_check = await self.sandbox.execute_command(
            session_id, 
            f"git ls-remote --heads origin {branch_name}", 
            working_dir=repo_path
        )
        
        if not push_check["stdout"].strip():
            # Branch not pushed, push it
            push_result = await self.sandbox.execute_command(
                session_id,
                f"git push -u origin {branch_name}",
                working_dir=repo_path
            )
            fixes.append({
                "action": "push_branch",
                "success": push_result["success"],
                "details": push_result.get("stderr", "") if not push_result["success"] else "Branch pushed successfully"
            })
        
        # Fix 2: Ensure GitHub CLI is authenticated
        auth_check = await self.sandbox.execute_command(
            session_id,
            "gh auth status",
            working_dir=repo_path
        )
        
        if not auth_check["success"]:
            auth_result = await self.sandbox.execute_command(
                session_id,
                f"echo '{github_token}' | gh auth login --with-token",
                working_dir=repo_path
            )
            fixes.append({
                "action": "authenticate_gh",
                "success": auth_result["success"],
                "details": auth_result.get("stderr", "") if not auth_result["success"] else "GH authenticated"
            })
        
        # Fix 3: Ensure remote is set correctly
        remote_check = await self.sandbox.execute_command(
            session_id,
            "git remote -v",
            working_dir=repo_path
        )
        
        if remote_check["success"]:
            remotes = remote_check["stdout"]
            if "https://" not in remotes and "git@" not in remotes:
                # Fix remote URL
                remote_fix = await self.sandbox.execute_command(
                    session_id,
                    "git remote set-url origin https://github.com/user/repo.git",
                    working_dir=repo_path
                )
                fixes.append({
                    "action": "fix_remote_url",
                    "success": remote_fix["success"],
                    "details": "Remote URL fixed"
                })
        
        return {
            "success": any(fix["success"] for fix in fixes),
            "fixes": fixes
        }
