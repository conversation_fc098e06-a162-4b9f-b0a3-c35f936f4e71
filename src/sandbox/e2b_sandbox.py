"""
E2B Sandbox Integration for Autonomous Coding Agent
Provides secure, isolated environment for code execution
"""

import logging
import uuid
from typing import Dict, Any
from e2b_code_interpreter import Sandbox
from ..utils.config import get_settings

logger = logging.getLogger(__name__)


class E2BSandbox:
    """E2B-based sandbox manager for secure code execution"""
    
    def __init__(self):
        self.settings = get_settings()
        self.active_sessions: Dict[str, Sandbox] = {}
        self.session_repo_paths: Dict[str, str] = {}  # Fix: Initialize repo paths storage
        self.session_tokens: Dict[str, str] = {}      # Fix: Initialize token storage
        
    async def create_session(self, session_id: str = None) -> str:
        """Create a new E2B sandbox session"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        try:
            # Create E2B sandbox instance
            # E2B timeout is in seconds and max is 1 hour (3600 seconds)
            timeout_seconds = min(self.settings.sandbox_timeout_minutes * 60, 3600)
            logger.info(f"Creating E2B sandbox with timeout: {timeout_seconds} seconds")
            
            sandbox = Sandbox(
                api_key=self.settings.e2b_api_key,
                timeout=timeout_seconds
            )
            
            # Store the session
            self.active_sessions[session_id] = sandbox
            
            # Initialize basic setup
            await self._initialize_sandbox(sandbox)
            
            logger.info(f"Created E2B sandbox session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create E2B sandbox session: {e}")
            # Try with default timeout if custom timeout fails
            try:
                logger.info("Retrying with default timeout (600 seconds)")
                sandbox = Sandbox(api_key=self.settings.e2b_api_key, timeout=600)
                self.active_sessions[session_id] = sandbox
                await self._initialize_sandbox(sandbox)
                logger.info(f"Created E2B sandbox session with default timeout: {session_id}")
                return session_id
            except Exception as e2:
                logger.error(f"Failed to create E2B sandbox with default timeout: {e2}")
                raise
    
    async def _initialize_sandbox(self, sandbox: Sandbox):
        """Initialize sandbox with basic tools"""
        try:
            # Install common dependencies
            result = sandbox.run_code("!pip install requests aiohttp")
            if result.error:
                logger.warning(f"Failed to install basic dependencies: {result.error}")
            
            # Set up git configuration
            sandbox.run_code("!git config --global user.email '<EMAIL>'")
            sandbox.run_code("!git config --global user.name 'Backspace Agent'")
            
        except Exception as e:
            logger.warning(f"Failed to initialize sandbox: {e}")
    
    async def execute_command(self, session_id: str, command: str, 
                            working_dir: str = None, timeout: int = 300) -> Dict[str, Any]:
        """Execute a command in the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Change directory if specified
            if working_dir:
                cd_result = sandbox.run_code(f"!cd {working_dir}")
                if cd_result.error:
                    return {
                        "success": False,
                        "error": f"Failed to change directory: {cd_result.error}"
                    }
            
            # Execute the command using ! prefix for shell commands
            if not command.startswith("!"):
                command = "!" + command
            
            result = sandbox.run_code(command)
            
            # Handle different result types from E2B
            if hasattr(result, 'stdout'):
                # Standard execution result
                return {
                    "success": not result.error,
                    "stdout": result.stdout if result.stdout else "",
                    "stderr": result.stderr if result.stderr else result.error if result.error else "",
                    "exit_code": 0 if not result.error else 1
                }
            elif hasattr(result, 'logs'):
                # For some E2B responses, logs contain stdout
                try:
                    stdout_logs = [log.line for log in result.logs.stdout if hasattr(log, 'line')]
                    stderr_logs = [log.line for log in result.logs.stderr if hasattr(log, 'line')]
                except AttributeError:
                    # Try direct logs access
                    stdout_logs = []
                    stderr_logs = []
                    if hasattr(result.logs, 'stdout'):
                        stdout_logs = [result.logs.stdout] if result.logs.stdout else []
                    if hasattr(result.logs, 'stderr'):
                        stderr_logs = [result.logs.stderr] if result.logs.stderr else []
                
                return {
                    "success": not result.error,
                    "stdout": '\n'.join(stdout_logs),
                    "stderr": '\n'.join(stderr_logs) if stderr_logs else (result.error if result.error else ""),
                    "exit_code": 0 if not result.error else 1
                }
            else:
                # Fallback for unknown result structure
                return {
                    "success": not result.error,
                    "stdout": str(result) if not result.error else "",
                    "stderr": result.error if result.error else "",
                    "exit_code": 0 if not result.error else 1
                }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Check if file exists
            check_result = sandbox.run_code(f"!test -f {file_path}")
            if check_result.error:
                return {"success": False, "error": "File not found"}
            
            # Read file content
            result = sandbox.run_code(f"!cat {file_path}")
            if not result.error:
                # Handle different result types from E2B
                if hasattr(result, 'stdout'):
                    content = result.stdout
                elif hasattr(result, 'logs'):
                    try:
                        stdout_logs = [log.line for log in result.logs.stdout if hasattr(log, 'line')]
                        content = '\n'.join(stdout_logs)
                    except AttributeError:
                        content = str(result.logs.stdout) if hasattr(result.logs, 'stdout') else str(result.logs)
                else:
                    content = str(result)
                return {"success": True, "content": content}
            else:
                return {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"File read failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write content to a file in the sandbox"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        sandbox = self.active_sessions[session_id]
        
        try:
            # Create directory if needed
            dir_path = "/".join(file_path.split("/")[:-1])
            if dir_path:
                sandbox.run_code(f"!mkdir -p {dir_path}")
            
            # Write file using cat with heredoc to handle special characters
            escaped_content = content.replace("'", "'\"'\"'")  # Escape single quotes
            result = sandbox.run_code(f"!cat > {file_path} << 'EOF'\n{escaped_content}\nEOF")
            
            if not result.error:
                logger.info(f"Successfully wrote to file: {file_path}")
                return {"success": True}
            else:
                return {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"File write failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def clone_repository(self, session_id: str, repo_url: str,
                             branch: str = "main") -> Dict[str, Any]:
        """Clone a repository in the sandbox with fixed working directory handling"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}

        try:
            # Extract repo name from URL
            repo_name = repo_url.split('/')[-1].replace('.git', '')

            # Use fixed absolute path for E2B stateless environment
            repo_path = f"/home/<USER>/{repo_name}"

            # Clone the repository to absolute path
            clone_result = await self.execute_command(
                session_id,
                f"git clone {repo_url} {repo_path}",
                timeout=120
            )

            if not clone_result["success"]:
                return clone_result

            # Store the absolute repo path for this session
            self.session_repo_paths[session_id] = repo_path

            # Checkout the specified branch using git -C (no working dir dependency)
            if branch != "main":
                await self.execute_command(session_id, f"git -C {repo_path} checkout {branch}")

            # Get file count using absolute path
            file_count_result = await self.execute_command(session_id, f"find {repo_path} -type f | wc -l")
            try:
                file_count_str = file_count_result.get("stdout", "0").strip()
                file_count = int(file_count_str) if file_count_str and file_count_str.isdigit() else 0
            except ValueError:
                file_count = 0

            logger.info(f"E2B: Cloned {repo_name} to {repo_path} with {file_count} files")

            return {
                "success": True,
                "repo_name": repo_name,
                "repo_path": repo_path,  # Include absolute path
                "branch": branch,
                "file_count": file_count
            }
            
        except Exception as e:
            logger.error(f"Repository clone failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def setup_git_auth(self, session_id: str, github_token: str) -> Dict[str, Any]:
        """Setup Git authentication for GitHub operations using secure method"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            # Store token securely for session
            self.session_tokens[session_id] = github_token

            # Method 1: GitHub CLI authentication (per research recommendations)
            logger.info("E2B: Attempting GitHub CLI authentication")
            gh_auth_result = await self.execute_command(
                session_id,
                f"echo '{github_token}' | gh auth login --with-token"
            )

            if gh_auth_result["success"]:
                logger.info("E2B: GitHub CLI authentication successful")
                # Still setup git config for completeness
                await self.execute_command(session_id, f"git config --global user.email '<EMAIL>'")
                await self.execute_command(session_id, f"git config --global user.name 'Backspace Agent'")
                return {"success": True, "method": "gh_cli"}

            # Method 2: Fallback to environment variable approach
            logger.info("E2B: GitHub CLI failed, using environment variable approach")
            await self.execute_command(session_id, f"git config --global user.email '<EMAIL>'")
            await self.execute_command(session_id, f"git config --global user.name 'Backspace Agent'")

            logger.info("E2B: Git authentication setup successful (env var method)")
            return {"success": True, "method": "env_var"}
                
        except Exception as e:
            logger.error(f"Git auth setup failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def push_branch(self, session_id: str, branch_name: str, repo_name: str = None) -> Dict[str, Any]:
        """Push a branch using git -C for reliable E2B operations (per research)"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}

        try:
            # Get absolute repo path from session storage
            repo_path = self.session_repo_paths.get(session_id)
            if not repo_path:
                return {"success": False, "error": "Repository path not found for session"}

            # Get GitHub token from secure storage
            github_token = self.session_tokens.get(session_id)
            if not github_token:
                return {"success": False, "error": "GitHub token not found for session"}

            logger.info(f"E2B: Pushing branch {branch_name} from {repo_path}")

            # Method 1: Try GitHub CLI (recommended by research)
            gh_push_result = await self.execute_command(
                session_id,
                f"cd {repo_path} && gh repo set-default && git push -u origin {branch_name}"
            )

            if gh_push_result["success"]:
                logger.info(f"E2B: Successfully pushed branch via GitHub CLI: {branch_name}")
                return {"success": True, "branch": branch_name, "method": "gh_cli"}

            # Method 2: Use git -C with environment variable (per research)
            logger.info("E2B: GitHub CLI push failed, trying git -C with env var")
            env_push_result = await self.execute_command(
                session_id,
                f"GITHUB_TOKEN={github_token} git -C {repo_path} push -u origin {branch_name}"
            )

            if env_push_result["success"]:
                logger.info(f"E2B: Successfully pushed branch via git -C: {branch_name}")
                return {"success": True, "branch": branch_name, "method": "git_c_env"}

            # Method 3: Fallback to authenticated URL (per research)
            logger.info("E2B: git -C failed, trying authenticated URL")

            # Get remote URL using git -C
            remote_result = await self.execute_command(
                session_id,
                f"git -C {repo_path} remote get-url origin"
            )

            if not remote_result["success"]:
                return {"success": False, "error": "Failed to get remote URL"}

            remote_url = remote_result["stdout"].strip()

            # Create authenticated URL
            if "https://github.com/" in remote_url:
                auth_url = remote_url.replace("https://github.com/", f"https://{github_token}@github.com/")
            else:
                return {"success": False, "error": "Unsupported remote URL format"}

            # Push using git -C with authenticated URL
            result = await self.execute_command(
                session_id,
                f"git -C {repo_path} push -u {auth_url} {branch_name}",
                timeout=120
            )

            if result["success"]:
                logger.info(f"E2B: Successfully pushed branch via auth URL: {branch_name}")
                return {"success": True, "branch": branch_name, "method": "auth_url"}
            else:
                logger.error(f"E2B: Failed to push branch: {result.get('stderr', result.get('error'))}")
                return {"success": False, "error": result.get('stderr', result.get('error'))}

        except Exception as e:
            logger.error(f"Branch push failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def create_pull_request(self, session_id: str, title: str,
                                body: str, branch_name: str, repo_name: str = None) -> Dict[str, Any]:
        """Create PR using GitHub CLI and git -C (per research recommendations)"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}

        try:
            # Get absolute repo path from session storage
            repo_path = self.session_repo_paths.get(session_id)
            if not repo_path:
                return {"success": False, "error": "Repository path not found for session"}

            # Get GitHub token from secure storage
            github_token = self.session_tokens.get(session_id)
            if not github_token:
                return {"success": False, "error": "GitHub token not found for session"}

            logger.info(f"E2B: Creating PR for repository at {repo_path}")

            # Method 1: GitHub CLI (recommended by research)
            logger.info("E2B: Attempting PR creation via GitHub CLI")
            gh_pr_result = await self.execute_command(
                session_id,
                f"cd {repo_path} && gh pr create --title '{title}' --body '{body}' --head {branch_name} --base main"
            )

            if gh_pr_result["success"]:
                # Extract PR URL from GitHub CLI output
                pr_url = None
                for line in gh_pr_result["stdout"].split('\n'):
                    if 'https://github.com' in line and '/pull/' in line:
                        pr_url = line.strip()
                        break

                if not pr_url:
                    pr_url = f"https://github.com/owner/repo/pulls"  # Fallback

                logger.info(f"E2B: PR created successfully via GitHub CLI: {pr_url}")
                return {
                    "success": True,
                    "pr_url": pr_url,
                    "method": "github_cli"
                }

            # Method 2: Fallback to curl with GitHub API (per research)
            logger.info("E2B: GitHub CLI failed, trying curl with GitHub API")

            # Get remote URL using git -C (stateless approach per research)
            remote_result = await self.execute_command(
                session_id,
                f"git -C {repo_path} remote get-url origin"
            )

            if not remote_result["success"] or not remote_result["stdout"].strip():
                return {"success": False, "error": "Failed to get remote URL"}

            remote_url = remote_result["stdout"].strip()
            logger.info(f"E2B: Remote URL: {remote_url}")

            # Parse GitHub repo info
            if "github.com" in remote_url:
                parts = remote_url.replace("https://github.com/", "").replace("**************:", "").replace(".git", "").split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                else:
                    return {"success": False, "error": "Invalid GitHub URL format"}
            else:
                return {"success": False, "error": "Not a GitHub repository"}

            # Create PR using GitHub API with curl
            escaped_title = title.replace('"', '\\"')
            escaped_body = body.replace('"', '\\"').replace('\n', '\\n')

            curl_cmd = f'''curl -X POST \
                -H "Authorization: token {github_token}" \
                -H "Accept: application/vnd.github.v3+json" \
                -H "Content-Type: application/json" \
                -d '{{"title": "{escaped_title}", "body": "{escaped_body}", "head": "{branch_name}", "base": "main"}}' \
                https://api.github.com/repos/{owner}/{repo}/pulls'''

            pr_result = await self.execute_command(session_id, curl_cmd)

            if pr_result["success"]:
                try:
                    import json
                    response_data = json.loads(pr_result["stdout"])
                    pr_url = response_data.get("html_url", f"https://github.com/{owner}/{repo}/pulls")
                    logger.info(f"E2B: PR created successfully via API: {pr_url}")
                    return {
                        "success": True,
                        "pr_url": pr_url,
                        "method": "github_api"
                    }
                except json.JSONDecodeError:
                    # Fallback URL if JSON parsing fails
                    pr_url = f"https://github.com/{owner}/{repo}/pulls"
                    logger.info(f"E2B: PR likely created, using fallback URL: {pr_url}")
                    return {
                        "success": True,
                        "pr_url": pr_url,
                        "method": "github_api_fallback"
                    }
            else:
                logger.error(f"E2B: PR creation failed: {pr_result.get('stderr', pr_result.get('error'))}")
                return {"success": False, "error": pr_result.get('stderr', pr_result.get('error'))}


                
        except Exception as e:
            logger.error(f"Pull request creation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup_session(self, session_id: str):
        """Clean up and close a sandbox session"""
        if session_id in self.active_sessions:
            try:
                sandbox = self.active_sessions[session_id]
                sandbox.kill()  # Use kill() instead of close() for E2B
                del self.active_sessions[session_id]
                # Clean up session storage
                self.session_repo_paths.pop(session_id, None)
                self.session_tokens.pop(session_id, None)
                logger.info(f"Cleaned up E2B sandbox session: {session_id}")
            except Exception as e:
                logger.error(f"Failed to cleanup session {session_id}: {e}")
    
    async def list_files(self, session_id: str, directory: str = ".") -> Dict[str, Any]:
        """List files in a directory"""
        return await self.execute_command(session_id, f"find {directory} -type f | head -20")
    
    async def get_file_content(self, session_id: str, file_path: str, 
                             max_lines: int = 100) -> Dict[str, Any]:
        """Get file content with optional line limit"""
        if max_lines:
            return await self.execute_command(session_id, f"head -n {max_lines} {file_path}")
        else:
            return await self.read_file(session_id, file_path)
    
    async def cleanup_all(self):
        """Clean up all active sessions"""
        for session_id in list(self.active_sessions.keys()):
            await self.cleanup_session(session_id)
    
    def __del__(self):
        """Cleanup all sessions on destruction"""
        for session_id in list(self.active_sessions.keys()):
            try:
                self.active_sessions[session_id].kill()
            except:
                pass