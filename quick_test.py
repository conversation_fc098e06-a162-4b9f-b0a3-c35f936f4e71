#!/usr/bin/env python3
import requests
import json
import time
from datetime import datetime

def test_e2b_sandbox():
    """Quick test to verify E2B sandbox is working"""
    
    # Generate unique branch name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    
    url = "http://localhost:8000/code"
    payload = {
        "repo_url": "https://github.com/alhridoy/CSDR",
        "prompt": f"Add API documentation with Swagger/OpenAPI integration. Include endpoint descriptions, request/response schemas, and example usage. This is a test at {timestamp}",
        "branch_name": f"api-docs-{timestamp}",
        "pr_title": f"Add API documentation with Swagger - Test {timestamp}"
    }
    
    print(f"🚀 Testing E2B Sandbox - {timestamp}")
    print(f"Branch: {payload['branch_name']}")
    print(f"URL: {payload['repo_url']}")
    print("=" * 50)
    
    try:
        # Test if server is running
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Server is running")
            health_data = health_response.json()
            print(f"   - Coding agent: {health_data.get('components', {}).get('coding_agent', 'Unknown')}")
            print(f"   - Sandbox manager: {health_data.get('components', {}).get('sandbox_manager', 'Unknown')}")
        else:
            print("❌ Server health check failed")
            return
    except:
        print("❌ Server is not running. Please start with: python -m uvicorn src.api.main:app --reload --port 8000")
        return
    
    print("\n📡 Sending PR creation request...")
    
    # Make the request
    response = requests.post(url, json=payload, stream=True, timeout=300)
    
    if response.status_code != 200:
        print(f"❌ Request failed: {response.status_code} - {response.text}")
        return
    
    print("✅ Request accepted - Processing events:")
    print("-" * 30)
    
    pr_created = False
    pr_url = None
    events_count = 0
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                try:
                    data = json.loads(line_str[6:])
                    events_count += 1
                    
                    event_type = data.get('type', 'Unknown')
                    
                    if event_type == 'Status':
                        print(f"📊 {data.get('message', '')}")
                    elif event_type == 'Tool: Bash':
                        command = data.get('command', '')
                        if 'git' in command:
                            print(f"🔧 Git: {command}")
                    elif event_type == 'AI Message':
                        message = data.get('message', '')
                        if len(message) > 100:
                            message = message[:100] + "..."
                        print(f"🤖 {message}")
                    elif 'pr_created' in str(data) or 'pr_url' in str(data):
                        pr_created = True
                        pr_url = data.get('pr_url', 'URL not found')
                        print(f"🎉 PR CREATED: {pr_url}")
                    elif 'error' in data:
                        print(f"❌ Error: {data.get('error_message', data)}")
                    
                except json.JSONDecodeError:
                    if 'pr_created' in line_str:
                        pr_created = True
                        print(f"🎉 PR Creation detected in: {line_str}")
    
    print("\n" + "=" * 50)
    print(f"📈 Processed {events_count} events")
    
    if pr_created:
        print("✅ SUCCESS: PR was created!")
        if pr_url:
            print(f"🔗 PR URL: {pr_url}")
        print(f"📋 Check: https://github.com/alhridoy/CSDR/pulls")
    else:
        print("❌ FAILED: No PR creation detected")
    
    print(f"\n🕐 Test completed at {datetime.now()}")

if __name__ == "__main__":
    test_e2b_sandbox()