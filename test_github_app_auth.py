#!/usr/bin/env python3
"""
Test GitHub App authentication with gh-token extension
"""
import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sandbox.e2b_sandbox import E2BSandbox
from src.utils.config import get_settings

async def test_github_app_auth():
    """Test GitHub App authentication using gh-token"""
    print("🚀 Testing GitHub App Authentication with gh-token extension...\n")
    
    # Check if GitHub App credentials are configured
    settings = get_settings()
    
    print("🔍 Checking GitHub App configuration...")
    app_id = getattr(settings, 'github_app_id', None)
    installation_id = getattr(settings, 'github_app_installation_id', None)
    private_key = getattr(settings, 'github_app_private_key', None)
    
    if not all([app_id, installation_id, private_key]):
        print("⚠️  GitHub App credentials not configured. Add to .env:")
        print("   GITHUB_APP_ID=your_app_id")
        print("   GITHUB_APP_INSTALLATION_ID=your_installation_id")
        print("   GITHUB_APP_PRIVATE_KEY='-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----'")
        print("\n📝 To create a GitHub App:")
        print("   1. Go to GitHub Settings > Developer settings > GitHub Apps")
        print("   2. Click 'New GitHub App'")
        print("   3. Configure permissions (Contents: Read & Write, Pull requests: Write)")
        print("   4. Install the app on your repositories")
        print("   5. Get the App ID, Installation ID, and download private key")
        print("\n🔄 Falling back to PAT authentication test...")
        return await test_pat_auth()
    
    print(f"✅ GitHub App ID: {app_id}")
    print(f"✅ Installation ID: {installation_id}")
    print(f"✅ Private Key: {'*' * 20}...{private_key[-20:] if len(private_key) > 40 else '*' * len(private_key)}")
    
    try:
        # Create E2B sandbox session
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        print(f"✅ E2B session created: {session_id}")
        
        # Test GitHub App authentication
        print("\n🔐 Testing GitHub App authentication...")
        auth_result = await sandbox.setup_git_auth(
            session_id,
            settings.github_token,  # Fallback PAT
            app_id=app_id,
            installation_id=installation_id,
            private_key_content=private_key
        )
        
        print(f"✅ Auth result: {auth_result}")
        
        if auth_result.get('success') and auth_result.get('method') == 'github_app':
            print("🎉 GitHub App authentication successful!")
            
            # Test GitHub CLI with app token
            gh_status = await sandbox.execute_command(session_id, "gh auth status")
            print(f"✅ GitHub CLI status: {gh_status.get('success')}")
            
            # Test repository access
            test_repo = "https://github.com/alhridoy/browser_agent"
            clone_result = await sandbox.clone_repository(session_id, test_repo)
            
            if clone_result.get('success'):
                print(f"✅ Repository access successful: {clone_result.get('file_count')} files")
                
                # Test creating a branch (this requires write access)
                repo_path = clone_result.get('repo_path')
                branch_result = await sandbox.execute_command(
                    session_id,
                    f"git -C {repo_path} checkout -b test-github-app-auth"
                )
                
                if branch_result.get('success'):
                    print("✅ Branch creation successful - GitHub App has write access!")
                else:
                    print(f"⚠️  Branch creation failed: {branch_result.get('stderr')}")
            else:
                print(f"❌ Repository access failed: {clone_result.get('error')}")
        
        elif auth_result.get('success') and auth_result.get('method') == 'gh_cli':
            print("✅ Fell back to PAT authentication (GitHub App failed)")
        else:
            print(f"❌ Authentication failed: {auth_result.get('error')}")
        
        # Cleanup
        await sandbox.cleanup_session(session_id)
        print("✅ Session cleaned up")
        
        return auth_result.get('success', False)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pat_auth():
    """Test regular PAT authentication"""
    print("\n🔐 Testing PAT authentication...")
    
    try:
        settings = get_settings()
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Test PAT authentication
        auth_result = await sandbox.setup_git_auth(session_id, settings.github_token)
        print(f"✅ PAT auth result: {auth_result}")
        
        await sandbox.cleanup_session(session_id)
        return auth_result.get('success', False)
        
    except Exception as e:
        print(f"❌ PAT test failed: {e}")
        return False

def print_setup_instructions():
    """Print setup instructions for GitHub App"""
    print("\n" + "="*60)
    print("📋 GITHUB APP SETUP INSTRUCTIONS")
    print("="*60)
    
    print("\n1. Create a GitHub App:")
    print("   • Go to https://github.com/settings/apps")
    print("   • Click 'New GitHub App'")
    print("   • Fill in basic information (name, description, homepage URL)")
    
    print("\n2. Configure Permissions:")
    print("   • Repository permissions:")
    print("     - Contents: Read & Write")
    print("     - Metadata: Read")
    print("     - Pull requests: Write")
    print("     - Issues: Write (optional)")
    
    print("\n3. Install the App:")
    print("   • After creating, click 'Install App'")
    print("   • Choose repositories to install on")
    print("   • Note the Installation ID from the URL")
    
    print("\n4. Get Credentials:")
    print("   • App ID: Found in app settings")
    print("   • Installation ID: From installation URL")
    print("   • Private Key: Generate and download from app settings")
    
    print("\n5. Add to .env file:")
    print("   GITHUB_APP_ID=123456")
    print("   GITHUB_APP_INSTALLATION_ID=********")
    print("   GITHUB_APP_PRIVATE_KEY='-----BEGIN RSA PRIVATE KEY-----")
    print("   MIIEpAIBAAKCAQEA...")
    print("   -----END RSA PRIVATE KEY-----'")
    
    print("\n6. Benefits of GitHub App vs PAT:")
    print("   ✅ More granular permissions")
    print("   ✅ Better security (short-lived tokens)")
    print("   ✅ Organization-level control")
    print("   ✅ Audit trail and monitoring")
    print("   ✅ No user account dependency")

async def main():
    print("🔧 GitHub App Authentication Test")
    print("=" * 40)
    
    success = await test_github_app_auth()
    
    if success:
        print("\n🎉 Authentication test completed successfully!")
    else:
        print("\n❌ Authentication test failed")
        print_setup_instructions()

if __name__ == "__main__":
    asyncio.run(main())
