#!/usr/bin/env python3
import requests
import json
import sys

def test_pr_creation():
    """Test PR creation with the fixed E2B sandbox"""
    
    url = "http://localhost:8000/code"
    
    payload = {
        "repo_url": "https://github.com/alhridoy/CSDR",
        "prompt": "Add comprehensive logging system with different log levels (DEBUG, INFO, WARN, ERROR). Include timestamp, request ID tracking, and structured logging for better debugging and monitoring.",
        "branch_name": "feature-logging-system",
        "pr_title": "Add comprehensive logging system with structured logging"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("🚀 Testing E2B sandbox with PR creation...")
    print(f"Repository: {payload['repo_url']}")
    print(f"Branch: {payload['branch_name']}")
    print(f"Prompt: {payload['prompt'][:100]}...")
    print("\n" + "="*60 + "\n")
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True, timeout=300)
        
        if response.status_code == 200:
            print("✅ Request successful - streaming events:")
            print("-" * 40)
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])  # Remove 'data: ' prefix
                            event_type = data.get('type', 'Unknown')
                            
                            if event_type == 'Status':
                                print(f"📊 {data.get('message', '')}")
                            elif event_type == 'Tool: Bash':
                                command = data.get('command', '')
                                output = data.get('output', '')
                                print(f"🔧 {command}")
                                if output.strip():
                                    print(f"   Output: {output.strip()}")
                            elif event_type == 'AI Message':
                                print(f"🤖 {data.get('message', '')}")
                            elif event_type == 'Tool: Read':
                                print(f"📖 Reading: {data.get('filepath', '')}")
                            elif event_type == 'Tool: Edit':
                                print(f"✏️  Editing: {data.get('filepath', '')}")
                            elif 'error' in data:
                                print(f"❌ Error: {data}")
                            else:
                                print(f"ℹ️  {event_type}: {data}")
                                
                        except json.JSONDecodeError:
                            print(f"Raw: {line_str}")
            
            print("\n" + "="*60)
            print("✅ Stream completed successfully!")
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏱️  Request timed out - this is normal for long-running operations")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_pr_creation()