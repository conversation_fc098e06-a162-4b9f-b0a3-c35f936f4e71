#!/usr/bin/env python3
"""
Test GitHub CLI PR creation
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sandbox.e2b_sandbox import E2BSandbox
from src.utils.config import get_settings

async def test_gh_cli():
    """Test GitHub CLI PR creation step by step"""
    
    print("🔍 Testing GitHub CLI PR Creation")
    print("=" * 50)
    
    try:
        settings = get_settings()
        sandbox = E2BSandbox()
        
        # Create session and clone
        session_id = await sandbox.create_session()
        repo_result = await sandbox.clone_repository(session_id, "https://github.com/alhridoy/browser_agent")
        repo_path = repo_result.get('repo_path')
        
        # Setup git auth
        await sandbox.setup_git_auth(session_id, settings.github_token)
        
        print(f"✅ Setup complete, testing from: {repo_path}")
        
        # Test GitHub CLI availability
        gh_version = await sandbox.execute_command(session_id, "gh --version")
        print(f"🔧 GitHub CLI available: {gh_version.get('success')}")
        if gh_version.get('success'):
            print(f"   Version: {gh_version.get('stdout', '').strip()}")
        
        # Test GitHub CLI auth status
        gh_auth = await sandbox.execute_command(session_id, "gh auth status")
        print(f"🔐 GitHub CLI auth: {gh_auth.get('success')}")
        print(f"   Auth output: {gh_auth.get('stdout', '')}")
        print(f"   Auth error: {gh_auth.get('stderr', '')}")
        
        # Create a test branch and make a small change
        branch_name = "test-pr-creation-123"
        
        # Create branch
        branch_result = await sandbox.execute_command(session_id, f"git -C {repo_path} checkout -b {branch_name}")
        print(f"🌿 Branch created: {branch_result.get('success')}")
        
        # Make a small change
        change_result = await sandbox.execute_command(
            session_id, 
            f"echo '# Test PR Creation' >> {repo_path}/test-file.md"
        )
        print(f"📝 Change made: {change_result.get('success')}")
        
        # Commit the change
        add_result = await sandbox.execute_command(session_id, f"git -C {repo_path} add .")
        commit_result = await sandbox.execute_command(session_id, f"git -C {repo_path} commit -m 'Test PR creation'")
        print(f"💾 Committed: {commit_result.get('success')}")
        
        # Push the branch
        push_result = await sandbox.execute_command(session_id, f"git -C {repo_path} push -u origin {branch_name}")
        print(f"⬆️  Pushed: {push_result.get('success')}")
        if not push_result.get('success'):
            print(f"   Push error: {push_result.get('stderr', '')}")
        
        # Try to create PR with GitHub CLI
        if push_result.get('success'):
            print(f"\n🔄 Testing GitHub CLI PR creation...")
            
            pr_result = await sandbox.execute_command(
                session_id,
                f"cd {repo_path} && gh pr create --title 'Test PR Creation' --body 'Testing automated PR creation' --head {branch_name} --base main"
            )
            
            print(f"📋 PR creation success: {pr_result.get('success')}")
            print(f"📋 PR stdout: {pr_result.get('stdout', '')}")
            print(f"📋 PR stderr: {pr_result.get('stderr', '')}")
            
            if pr_result.get('success'):
                # Look for PR URL in output
                stdout = pr_result.get('stdout', '')
                for line in stdout.split('\n'):
                    if 'https://github.com' in line and '/pull/' in line:
                        print(f"🎉 PR URL found: {line.strip()}")
                        break
                else:
                    print("❌ No PR URL found in output")
        
        await sandbox.cleanup_session(session_id)
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_gh_cli())
    sys.exit(0 if success else 1)
