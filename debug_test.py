#!/usr/bin/env python3
"""
Comprehensive debug test for PR creation issues
"""
import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sandbox.e2b_sandbox import E2BSandbox
from src.utils.config import get_settings

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_e2b_basic():
    """Test basic E2B functionality"""
    print("🔍 Testing E2B basic functionality...")
    
    try:
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        print(f"✅ E2B session created: {session_id}")
        
        # Test basic command
        result = await sandbox.execute_command(session_id, "echo 'Hello E2B'")
        print(f"✅ Basic command result: {result}")
        
        await sandbox.cleanup_session(session_id)
        print("✅ E2B basic functionality works")
        return True
        
    except Exception as e:
        print(f"❌ E2B basic test failed: {e}")
        return False

async def test_git_operations():
    """Test git operations in E2B"""
    print("\n🔍 Testing git operations...")
    
    try:
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Clone repository
        repo_result = await sandbox.clone_repository(
            session_id, 
            "https://github.com/alhridoy/browser_agent"
        )
        print(f"✅ Repository clone result: {repo_result}")
        
        if repo_result["success"]:
            # Test git remote command
            remote_result = await sandbox.execute_command(
                session_id,
                f"git -C {repo_result['repo_path']} remote get-url origin"
            )
            print(f"✅ Git remote result: {remote_result}")
            
            # Test git status
            status_result = await sandbox.execute_command(
                session_id,
                f"git -C {repo_result['repo_path']} status"
            )
            print(f"✅ Git status result: {status_result}")
        
        await sandbox.cleanup_session(session_id)
        return True
        
    except Exception as e:
        print(f"❌ Git operations test failed: {e}")
        return False

async def test_github_auth():
    """Test GitHub authentication"""
    print("\n🔍 Testing GitHub authentication...")
    
    try:
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Get GitHub token from settings
        settings = get_settings()
        github_token = settings.github_token
        
        # Test GitHub CLI auth
        auth_result = await sandbox.setup_git_auth(session_id, github_token)
        print(f"✅ GitHub auth result: {auth_result}")
        
        # Test GitHub CLI command
        gh_result = await sandbox.execute_command(session_id, "gh auth status")
        print(f"✅ GitHub CLI status: {gh_result}")
        
        await sandbox.cleanup_session(session_id)
        return True
        
    except Exception as e:
        print(f"❌ GitHub auth test failed: {e}")
        return False

async def test_pr_creation_debug():
    """Debug PR creation step by step"""
    print("\n🔍 Debugging PR creation step by step...")
    
    try:
        sandbox = E2BSandbox()
        session_id = await sandbox.create_session()
        
        # Clone repository
        repo_result = await sandbox.clone_repository(
            session_id, 
            "https://github.com/alhridoy/browser_agent"
        )
        
        if not repo_result["success"]:
            print(f"❌ Repository clone failed: {repo_result}")
            return False
            
        repo_path = repo_result["repo_path"]
        print(f"✅ Repository cloned to: {repo_path}")
        
        # Setup GitHub auth
        settings = get_settings()
        auth_result = await sandbox.setup_git_auth(session_id, settings.github_token)
        print(f"✅ GitHub auth setup: {auth_result}")
        
        # Create a test branch
        branch_name = "debug-pr-test"
        branch_result = await sandbox.execute_command(
            session_id,
            f"git -C {repo_path} checkout -b {branch_name}"
        )
        print(f"✅ Branch creation: {branch_result}")
        
        # Make a small change
        change_result = await sandbox.execute_command(
            session_id,
            f"echo '# Debug test' >> {repo_path}/DEBUG_TEST.md"
        )
        print(f"✅ File change: {change_result}")
        
        # Add and commit
        add_result = await sandbox.execute_command(session_id, f"git -C {repo_path} add .")
        commit_result = await sandbox.execute_command(
            session_id, 
            f"git -C {repo_path} commit -m 'Debug test commit'"
        )
        print(f"✅ Add result: {add_result}")
        print(f"✅ Commit result: {commit_result}")
        
        # Push branch
        push_result = await sandbox.push_branch(session_id, branch_name)
        print(f"✅ Push result: {push_result}")
        
        # Try PR creation
        pr_result = await sandbox.create_pull_request(
            session_id,
            "Debug PR Test",
            "This is a debug test PR",
            branch_name
        )
        print(f"✅ PR creation result: {pr_result}")
        
        await sandbox.cleanup_session(session_id)
        return True
        
    except Exception as e:
        print(f"❌ PR creation debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 Running comprehensive debug tests...\n")
    
    tests = [
        ("E2B Basic", test_e2b_basic),
        ("Git Operations", test_git_operations),
        ("GitHub Auth", test_github_auth),
        ("PR Creation Debug", test_pr_creation_debug),
    ]
    
    results = {}
    for name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {name}")
        print('='*50)
        results[name] = await test_func()
    
    print(f"\n{'='*50}")
    print("📊 SUMMARY")
    print('='*50)
    for name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {name}")
    
    if all(results.values()):
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check details above.")

if __name__ == "__main__":
    asyncio.run(main())
