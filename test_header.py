#!/usr/bin/env python3
import requests
import json
import time
from datetime import datetime

def test_header_component():
    """Test E2B sandbox by adding a header.tsx component"""
    
    # Generate unique branch name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    
    url = "http://localhost:8000/code"
    payload = {
        "repo_url": "https://github.com/alhridoy/CSDR",
        "prompt": """Create a professional React header component (header.tsx) with the following features:
        - Modern, responsive design with navigation menu
        - Logo/brand section on the left
        - Navigation links (Home, About, Services, Contact) in the center
        - User account dropdown or login button on the right
        - Mobile-friendly hamburger menu for smaller screens
        - TypeScript interface for props
        - Styled with Tailwind CSS or styled-components
        - Include proper accessibility attributes (ARIA labels)
        - Add hover effects and smooth transitions
        - Make it reusable and configurable""",
        "branch_name": f"add-header-component-{timestamp}",
        "pr_title": f"Add professional React header component - {timestamp}"
    }
    
    print("🚀 Testing E2B Sandbox - Creating Header Component")
    print("=" * 60)
    print(f"📅 Timestamp: {timestamp}")
    print(f"🌿 Branch: {payload['branch_name']}")
    print(f"🔗 Repository: {payload['repo_url']}")
    print(f"📝 Task: Creating header.tsx component")
    print("=" * 60)
    
    try:
        # Test server health first
        print("🔍 Checking server health...")
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Server is healthy")
            print(f"   - Status: {health_data.get('status')}")
            print(f"   - Coding Agent: {health_data.get('components', {}).get('coding_agent')}")
            print(f"   - Sandbox Manager: {health_data.get('components', {}).get('sandbox_manager')}")
        else:
            print(f"❌ Server health check failed: {health_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure server is running: python -m uvicorn src.api.main:app --reload --port 8000")
        return False
    
    print("\n📡 Sending PR creation request...")
    
    try:
        # Send the request
        response = requests.post(url, json=payload, stream=True, timeout=300)
        
        if response.status_code != 200:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        print("✅ Request accepted - Processing events:")
        print("-" * 50)
        
        # Track important events
        events = {
            'session_initialized': False,
            'repository_cloned': False,
            'files_found': 0,
            'ai_started': False,
            'branch_created': False,
            'files_edited': [],
            'committed': False,
            'pushed': False,
            'pr_created': False,
            'pr_url': None,
            'errors': []
        }
        
        # Process streaming response
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        event_type = data.get('type', 'Unknown')
                        
                        if event_type == 'Status':
                            message = data.get('message', '')
                            print(f"📊 {message}")
                            
                            if 'Initializing coding session' in message:
                                events['session_initialized'] = True
                            elif 'Repository cloned successfully' in message:
                                events['repository_cloned'] = True
                                # Extract file count
                                if 'Found' in message:
                                    try:
                                        events['files_found'] = int(message.split('Found')[1].split('files')[0].strip())
                                    except:
                                        pass
                            elif 'Starting AI coding agent' in message:
                                events['ai_started'] = True
                            elif 'Creating git branch' in message:
                                events['branch_created'] = True
                            elif 'Committing changes' in message:
                                events['committed'] = True
                            elif 'Pushing branch' in message:
                                events['pushed'] = True
                                
                        elif event_type == 'Tool: Bash':
                            command = data.get('command', '')
                            output = data.get('output', '')
                            
                            if 'git checkout -b' in command:
                                print(f"🌿 Branch: {command}")
                                if output.strip():
                                    print(f"   ✅ {output.strip()}")
                            elif 'git add' in command:
                                print(f"📦 Adding files: {command}")
                            elif 'git commit' in command:
                                print(f"💾 Committing: {command[:50]}...")
                            elif 'git push' in command:
                                print(f"🚀 Pushing: {command}")
                                
                        elif event_type == 'Tool: Edit':
                            filepath = data.get('filepath', '')
                            if filepath:
                                events['files_edited'].append(filepath)
                                print(f"✏️  Editing: {filepath}")
                                
                        elif event_type == 'Tool: Read':
                            filepath = data.get('filepath', '')
                            print(f"📖 Reading: {filepath}")
                            
                        elif event_type == 'AI Message':
                            message = data.get('message', '')
                            if len(message) > 100:
                                message = message[:100] + "..."
                            print(f"🤖 {message}")
                            
                        elif 'pr_created' in str(data).lower() or 'pr_url' in str(data):
                            events['pr_created'] = True
                            events['pr_url'] = data.get('pr_url', 'URL not found')
                            print(f"🎉 PR CREATED: {events['pr_url']}")
                            
                        elif 'error' in str(data).lower():
                            error_msg = data.get('error_message', str(data))
                            events['errors'].append(error_msg)
                            print(f"❌ Error: {error_msg}")
                            
                    except json.JSONDecodeError:
                        # Handle non-JSON lines
                        if 'pr_created' in line_str.lower():
                            events['pr_created'] = True
                            print(f"🎉 PR Creation detected: {line_str}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📋 EXECUTION SUMMARY")
        print("=" * 60)
        
        print(f"✅ Session Initialized: {events['session_initialized']}")
        print(f"✅ Repository Cloned: {events['repository_cloned']} ({events['files_found']} files)")
        print(f"✅ AI Agent Started: {events['ai_started']}")
        print(f"✅ Branch Created: {events['branch_created']}")
        print(f"✅ Files Edited: {len(events['files_edited'])} files")
        if events['files_edited']:
            for file in events['files_edited']:
                print(f"    - {file}")
        print(f"✅ Changes Committed: {events['committed']}")
        print(f"✅ Branch Pushed: {events['pushed']}")
        print(f"✅ PR Created: {events['pr_created']}")
        
        if events['pr_url']:
            print(f"🔗 PR URL: {events['pr_url']}")
            
        if events['errors']:
            print(f"❌ Errors: {len(events['errors'])}")
            for error in events['errors']:
                print(f"    - {error}")
        
        # Final verdict
        success = (events['session_initialized'] and 
                  events['repository_cloned'] and 
                  events['ai_started'] and 
                  events['branch_created'] and 
                  events['committed'] and 
                  events['pushed'] and 
                  events['pr_created'])
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 SUCCESS! Your E2B Sandbox is working perfectly!")
            print("✅ Header component should have been created")
            print("✅ PR should be visible at: https://github.com/alhridoy/CSDR/pulls")
            if events['pr_url']:
                print(f"✅ Direct PR link: {events['pr_url']}")
        else:
            print("❌ FAILED! Some steps didn't complete successfully")
            print("🔍 Check the errors above for debugging")
        
        print(f"🕐 Test completed at {datetime.now()}")
        return success
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_header_component()
    exit(0 if success else 1)